<template>
  <div class="listing-detail-page">
    <div class="page-container">
      <a-spin :loading="loading" style="width: 100%">
        <div v-if="listing" class="detail-content">
          <!-- 返回按钮 -->
          <div class="back-button">
            <a-button @click="goBack">
              <template #icon>
                <icon-left />
              </template>
              返回列表
            </a-button>
          </div>

          <!-- 房源基本信息 -->
          <a-card class="basic-info-card">
            <div class="basic-info">
              <div class="info-left">
                <div class="listing-image">
                  <img
                    :src="listing.imageUrl || '/placeholder-house.jpg'"
                    :alt="listing.title"
                    @error="handleImageError"
                  />
                  <div class="source-badge">
                    <a-tag :color="sourceColor">{{ sourceText }}</a-tag>
                  </div>
                </div>
              </div>
              
              <div class="info-right">
                <h1 class="listing-title">{{ listing.title }}</h1>
                
                <div class="location-info">
                  <a-space>
                    <icon-location class="location-icon" />
                    <span class="district">{{ listing.district }}</span>
                    <span class="community">{{ listing.community }}</span>
                  </a-space>
                </div>

                <div class="price-info">
                  <div class="total-price">
                    <span class="price-value">{{ listing.totalPrice }}</span>
                    <span class="price-unit">万</span>
                  </div>
                  <div class="unit-price">
                    {{ formatUnitPrice(listing.unitPrice) }}元/㎡
                  </div>
                </div>

                <div class="house-info">
                  <a-descriptions :column="2" size="medium">
                    <a-descriptions-item label="户型">
                      {{ listing.layout }}
                    </a-descriptions-item>
                    <a-descriptions-item label="面积">
                      {{ listing.area }}㎡
                    </a-descriptions-item>
                    <a-descriptions-item label="楼层" v-if="listing.floor">
                      {{ listing.floor }}
                    </a-descriptions-item>
                    <a-descriptions-item label="朝向" v-if="listing.orientation">
                      {{ listing.orientation }}
                    </a-descriptions-item>
                    <a-descriptions-item label="建造年份" v-if="listing.buildYear">
                      {{ listing.buildYear }}年
                    </a-descriptions-item>
                    <a-descriptions-item label="房源编号">
                      {{ listing.listingId }}
                    </a-descriptions-item>
                  </a-descriptions>
                </div>

                <div class="time-info">
                  <a-space direction="vertical">
                    <span>
                      <strong>首次发现:</strong> {{ formatTime(listing.firstScrapedAt) }}
                    </span>
                    <span>
                      <strong>最后更新:</strong> {{ formatTime(listing.lastScrapedAt) }}
                    </span>
                  </a-space>
                </div>

                <div class="action-buttons">
                  <a-space>
                    <a-button
                      type="primary"
                      @click="handleSubscribe"
                      :loading="subscribeLoading"
                    >
                      <template #icon>
                        <icon-heart />
                      </template>
                      关注价格变动
                    </a-button>
                    <a-button @click="handleOpenLink">
                      <template #icon>
                        <icon-link />
                      </template>
                      查看原链接
                    </a-button>
                  </a-space>
                </div>
              </div>
            </div>
          </a-card>

          <!-- 价格历史图表 -->
          <a-card title="价格历史" class="price-history-card">
            <div v-if="hasPriceHistory" class="price-chart-container">
              <price-chart :price-history="listing.priceHistory" />
            </div>
            <a-empty v-else description="暂无价格历史数据" />
          </a-card>

          <!-- 价格历史表格 -->
          <a-card title="价格变动记录" class="price-table-card">
            <a-table
              :data="listing.priceHistory"
              :pagination="false"
              :scroll="{ y: 400 }"
            >
              <template #columns>
                <a-table-column
                  title="价格"
                  data-index="price"
                  :width="120"
                >
                  <template #cell="{ record }">
                    <span class="price-cell">{{ record.price }}万</span>
                  </template>
                </a-table-column>
                <a-table-column
                  title="变动"
                  :width="120"
                >
                  <template #cell="{ record, rowIndex }">
                    <span v-if="rowIndex < listing.priceHistory.length - 1" class="price-change">
                      {{ getPriceChange(record, rowIndex) }}
                    </span>
                    <span v-else class="initial-price">首次记录</span>
                  </template>
                </a-table-column>
                <a-table-column
                  title="记录时间"
                  data-index="scrapedAt"
                >
                  <template #cell="{ record }">
                    {{ formatTime(record.scrapedAt) }}
                  </template>
                </a-table-column>
              </template>
            </a-table>
          </a-card>
        </div>
        
        <a-empty v-else description="房源不存在或已下架" />
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import {
  IconLeft,
  IconLocation,
  IconHeart,
  IconLink
} from '@arco-design/web-vue/es/icon';
import { useListingsStore } from '../store/listings';
import { useSubscriptionsStore } from '../store/subscriptions';
import PriceChart from '../components/PriceChart.vue';

const route = useRoute();
const router = useRouter();
const listingsStore = useListingsStore();
const subscriptionsStore = useSubscriptionsStore();

// 状态
const subscribeLoading = ref(false);

// 计算属性
const listing = computed(() => listingsStore.currentListing);
const loading = computed(() => listingsStore.loading);

const sourceColor = computed(() => {
  if (!listing.value) return 'gray';
  switch (listing.value.source) {
    case 'beike':
      return 'green';
    case 'anjuke':
      return 'blue';
    default:
      return 'gray';
  }
});

const sourceText = computed(() => {
  if (!listing.value) return '';
  switch (listing.value.source) {
    case 'beike':
      return '贝壳网';
    case 'anjuke':
      return '安居客';
    default:
      return listing.value.source;
  }
});

const hasPriceHistory = computed(() => {
  return listing.value?.priceHistory && listing.value.priceHistory.length > 1;
});

// 格式化单价
const formatUnitPrice = (price: number): string => {
  return price.toLocaleString();
};

// 格式化时间
const formatTime = (timeStr: string): string => {
  return new Date(timeStr).toLocaleString();
};

// 计算价格变动
const getPriceChange = (record: any, index: number): string => {
  if (!listing.value || index >= listing.value.priceHistory.length - 1) {
    return '';
  }
  
  const currentPrice = record.price;
  const previousPrice = listing.value.priceHistory[index + 1].price;
  const change = currentPrice - previousPrice;
  
  if (change > 0) {
    return `+${change.toFixed(1)}万`;
  } else if (change < 0) {
    return `${change.toFixed(1)}万`;
  } else {
    return '无变动';
  }
};

// 事件处理
const goBack = () => {
  router.go(-1);
};

const handleSubscribe = async () => {
  if (!listing.value) return;
  
  try {
    subscribeLoading.value = true;
    await subscriptionsStore.createSubscription({
      subscriptionType: 'PRICE_CHANGE',
      listingId: listing.value.id
    });
    Message.success('已关注该房源的价格变动');
  } catch (error) {
    Message.error('关注失败，请重试');
  } finally {
    subscribeLoading.value = false;
  }
};

const handleOpenLink = () => {
  if (listing.value) {
    window.open(listing.value.link, '_blank');
  }
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = '/placeholder-house.jpg';
};

// 页面挂载时加载数据
onMounted(() => {
  const id = route.params.id as string;
  if (id) {
    listingsStore.fetchListingDetail(id);
  }
});
</script>

<style scoped>
.listing-detail-page {
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
  padding: 24px 0;
}

.back-button {
  margin-bottom: 24px;
}

.basic-info-card {
  margin-bottom: 24px;
}

.basic-info {
  display: flex;
  gap: 32px;
}

.info-left {
  flex-shrink: 0;
}

.listing-image {
  position: relative;
  width: 400px;
  height: 300px;
  border-radius: 8px;
  overflow: hidden;
}

.listing-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.source-badge {
  position: absolute;
  top: 12px;
  right: 12px;
}

.info-right {
  flex: 1;
}

.listing-title {
  font-size: 28px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 16px 0;
}

.location-info {
  margin-bottom: 24px;
  font-size: 16px;
}

.location-icon {
  color: #165dff;
  font-size: 18px;
}

.district {
  font-weight: 600;
  color: #4e5969;
}

.community {
  color: #86909c;
}

.price-info {
  margin-bottom: 32px;
}

.total-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

.price-value {
  font-size: 48px;
  font-weight: 700;
  color: #f53f3f;
}

.price-unit {
  font-size: 20px;
  color: #f53f3f;
  margin-left: 8px;
}

.unit-price {
  font-size: 18px;
  color: #86909c;
}

.house-info {
  margin-bottom: 24px;
}

.time-info {
  margin-bottom: 32px;
  color: #86909c;
}

.price-history-card,
.price-table-card {
  margin-bottom: 24px;
}

.price-chart-container {
  height: 400px;
}

.price-cell {
  font-weight: 600;
  color: #f53f3f;
}

.price-change {
  font-weight: 600;
}

.initial-price {
  color: #86909c;
  font-style: italic;
}

@media (max-width: 768px) {
  .basic-info {
    flex-direction: column;
    gap: 24px;
  }
  
  .listing-image {
    width: 100%;
    height: 250px;
  }
  
  .listing-title {
    font-size: 24px;
  }
  
  .price-value {
    font-size: 36px;
  }
}
</style>
