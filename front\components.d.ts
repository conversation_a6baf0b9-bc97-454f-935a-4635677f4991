/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    DistrictPriceChart: typeof import('./src/components/DistrictPriceChart.vue')['default']
    FilterDrawer: typeof import('./src/components/FilterDrawer.vue')['default']
    ListingCard: typeof import('./src/components/ListingCard.vue')['default']
    Navbar: typeof import('./src/components/Navbar.vue')['default']
    PriceChart: typeof import('./src/components/PriceChart.vue')['default']
    PriceTrendChart: typeof import('./src/components/PriceTrendChart.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SourceDistributionChart: typeof import('./src/components/SourceDistributionChart.vue')['default']
  }
}
