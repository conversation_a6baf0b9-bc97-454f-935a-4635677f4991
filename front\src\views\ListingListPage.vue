<template>
  <div class="listing-list-page">
    <div class="page-container ">
      <!-- 页面标题和操作 -->
      <div class="page-header ">
        <div class="header-content">
          <h1 class="page-title ">房源列表</h1>
          <div class="header-actions">
            <a-space>
              <a-button @click="handleRefresh" :loading="loading">
                <template #icon>
                  <icon-refresh />
                </template>
                刷新
              </a-button>
              <a-button type="primary" @click="showFilterDrawer = true">
                <template #icon>
                  <icon-filter />
                </template>
                筛选
              </a-button>
            </a-space>
          </div>
        </div>
      </div>

      <!-- 筛选条件显示 -->
      <div v-if="hasActiveFilters" class="active-filters">
        <a-space wrap>
          <span class="filter-label">当前筛选:</span>
          <a-tag
            v-if="filters.source"
            closable
            @close="removeFilter('source')"
          >
            来源: {{ getSourceName(filters.source) }}
          </a-tag>
          <a-tag
            v-if="filters.district"
            closable
            @close="removeFilter('district')"
          >
            区域: {{ filters.district }}
          </a-tag>
          <a-tag
            v-if="filters.community"
            closable
            @close="removeFilter('community')"
          >
            小区: {{ filters.community }}
          </a-tag>
          <a-tag
            v-if="filters.minPrice || filters.maxPrice"
            closable
            @close="removePriceFilter"
          >
            价格: {{ formatPriceRange() }}
          </a-tag>
          <a-tag
            v-if="filters.minArea || filters.maxArea"
            closable
            @close="removeAreaFilter"
          >
            面积: {{ formatAreaRange() }}
          </a-tag>
          <a-tag
            v-if="filters.layout"
            closable
            @close="removeFilter('layout')"
          >
            户型: {{ filters.layout }}
          </a-tag>
          <a-button
            type="text"
            size="small"
            @click="clearAllFilters"
          >
            清空筛选
          </a-button>
        </a-space>
      </div>

      <!-- 排序和视图切换 -->
      <div class="list-controls">
        <div class="sort-controls">
          <a-space>
            <span>排序:</span>
            <a-select
              v-model="sortBy"
              placeholder="选择排序字段"
              style="width: 150px"
              @change="handleSortChange"
            >
              <a-option value="lastScrapedAt">更新时间</a-option>
              <a-option value="totalPrice">总价</a-option>
              <a-option value="unitPrice">单价</a-option>
              <a-option value="area">面积</a-option>
              <a-option value="firstScrapedAt">发布时间</a-option>
            </a-select>
            <a-select
              v-model="sortOrder"
              style="width: 100px"
              @change="handleSortChange"
            >
              <a-option value="desc">降序</a-option>
              <a-option value="asc">升序</a-option>
            </a-select>
          </a-space>
        </div>
        <div class="view-controls">
          <a-radio-group v-model="viewMode" type="button">
            <a-radio value="grid">
              <icon-apps />
            </a-radio>
            <a-radio value="list">
              <icon-list />
            </a-radio>
          </a-radio-group>
        </div>
      </div>

      <!-- 房源列表 -->
      <a-spin :loading="loading" style="width: 100%">
        <div v-if="hasListings" class="listings-container">
          <!-- 网格视图 -->
          <div v-if="viewMode === 'grid'" class="listings-grid">
            <listing-card
              v-for="listing in listings"
              :key="listing.id"
              :listing="listing"
            />
          </div>
          
          <!-- 列表视图 -->
          <div v-else class="listings-list">
            <a-list
              :data="listings"
              :pagination="false"
            >
              <template #item="{ item }">
                <listing-card :listing="item" />
              </template>
            </a-list>
          </div>
        </div>
        <a-empty v-else description="暂无房源数据" />
      </a-spin>

      <!-- 分页 -->
      <div v-if="hasListings" class="pagination-container">
        <a-pagination
          :current="pagination.page"
          :total="pagination.total"
          :page-size="pagination.limit"
          :show-total="true"
          :show-jumper="true"
          :show-page-size="true"
          :page-size-options="[10, 20, 50, 100]"
          @change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 筛选抽屉 -->
    <filter-drawer
      v-model:visible="showFilterDrawer"
      :filters="filters"
      @apply="handleApplyFilters"
      @reset="handleResetFilters"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import {
  IconRefresh,
  IconFilter,
  IconApps,
  IconList
} from '@arco-design/web-vue/es/icon';
import { useListingsStore } from '../store/listings';
import ListingCard from '../components/ListingCard.vue';
import FilterDrawer from '../components/FilterDrawer.vue';
import type { ListingFilters } from '../api/client';

const route = useRoute();
const router = useRouter();
const listingsStore = useListingsStore();

// 状态
const showFilterDrawer = ref(false);
const viewMode = ref<'grid' | 'list'>('grid');
const sortBy = ref('lastScrapedAt');
const sortOrder = ref<'asc' | 'desc'>('desc');

// 计算属性
const listings = computed(() => listingsStore.listings);
const loading = computed(() => listingsStore.loading);
const pagination = computed(() => listingsStore.pagination);
const filters = computed(() => listingsStore.filters);
const hasListings = computed(() => listingsStore.hasListings);

const hasActiveFilters = computed(() => {
  return !!(
    filters.value.source ||
    filters.value.district ||
    filters.value.community ||
    filters.value.minPrice ||
    filters.value.maxPrice ||
    filters.value.minArea ||
    filters.value.maxArea ||
    filters.value.layout
  );
});

// 获取数据源名称
const getSourceName = (source: string) => {
  switch (source) {
    case 'beike':
      return '贝壳网';
    case 'anjuke':
      return '安居客';
    default:
      return source;
  }
};

// 格式化价格范围
const formatPriceRange = () => {
  const min = filters.value.minPrice;
  const max = filters.value.maxPrice;
  if (min && max) {
    return `${min}-${max}万`;
  } else if (min) {
    return `${min}万以上`;
  } else if (max) {
    return `${max}万以下`;
  }
  return '';
};

// 格式化面积范围
const formatAreaRange = () => {
  const min = filters.value.minArea;
  const max = filters.value.maxArea;
  if (min && max) {
    return `${min}-${max}㎡`;
  } else if (min) {
    return `${min}㎡以上`;
  } else if (max) {
    return `${max}㎡以下`;
  }
  return '';
};

// 事件处理
const handleRefresh = () => {
  listingsStore.fetchListings();
};

const handleSortChange = () => {
  listingsStore.updateFilters({
    sortBy: sortBy.value,
    sortOrder: sortOrder.value
  });
  listingsStore.fetchListings();
};

const handlePageChange = (page: number) => {
  listingsStore.updateFilters({ page });
  listingsStore.fetchListings();
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

const handlePageSizeChange = (pageSize: number) => {
  listingsStore.updateFilters({ limit: pageSize, page: 1 });
  listingsStore.fetchListings();
};

const handleApplyFilters = (newFilters: Partial<ListingFilters>) => {
  listingsStore.updateFilters(newFilters);
  listingsStore.fetchListings();
  showFilterDrawer.value = false;
  Message.success('筛选条件已应用');
};

const handleResetFilters = () => {
  listingsStore.resetFilters();
  sortBy.value = 'lastScrapedAt';
  sortOrder.value = 'desc';
  listingsStore.fetchListings();
  showFilterDrawer.value = false;
  Message.success('筛选条件已重置');
};

const removeFilter = (key: keyof ListingFilters) => {
  const newFilters = { ...filters.value };
  delete newFilters[key];
  listingsStore.updateFilters(newFilters);
  listingsStore.fetchListings();
};

const removePriceFilter = () => {
  const newFilters = { ...filters.value };
  delete newFilters.minPrice;
  delete newFilters.maxPrice;
  listingsStore.updateFilters(newFilters);
  listingsStore.fetchListings();
};

const removeAreaFilter = () => {
  const newFilters = { ...filters.value };
  delete newFilters.minArea;
  delete newFilters.maxArea;
  listingsStore.updateFilters(newFilters);
  listingsStore.fetchListings();
};

const clearAllFilters = () => {
  handleResetFilters();
};

// 初始化数据
onMounted(() => {
  // 从URL查询参数恢复筛选条件
  const query = route.query;
  if (Object.keys(query).length > 0) {
    const urlFilters: Partial<ListingFilters> = {};
    
    if (query.source) urlFilters.source = query.source as string;
    if (query.district) urlFilters.district = query.district as string;
    if (query.community) urlFilters.community = query.community as string;
    if (query.minPrice) urlFilters.minPrice = Number(query.minPrice);
    if (query.maxPrice) urlFilters.maxPrice = Number(query.maxPrice);
    if (query.minArea) urlFilters.minArea = Number(query.minArea);
    if (query.maxArea) urlFilters.maxArea = Number(query.maxArea);
    if (query.layout) urlFilters.layout = query.layout as string;
    if (query.sortBy) sortBy.value = query.sortBy as string;
    if (query.sortOrder) sortOrder.value = query.sortOrder as 'asc' | 'desc';
    
    listingsStore.updateFilters(urlFilters);
  }
  
  listingsStore.fetchListings();
});

// 监听筛选条件变化，更新URL
watch(
  () => filters.value,
  (newFilters) => {
    const query: Record<string, any> = {};
    
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        query[key] = value;
      }
    });
    
    router.replace({ query });
  },
  { deep: true }
);
</script>

<style scoped>
.listing-list-page {
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e5e6eb;
  padding: 24px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
  margin: 0;
}

.active-filters {
  background: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e6eb;
}

.filter-label {
  color: #86909c;
  font-weight: 500;
}

.list-controls {
  background: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e6eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.listings-container {
  padding: 24px;
}

.listings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.listings-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  margin: 0 auto;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 24px;
  background: #fff;
  border-top: 1px solid #e5e6eb;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .list-controls {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .listings-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
