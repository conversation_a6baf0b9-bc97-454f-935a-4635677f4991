<template>
  <a-card
    class="listing-card"
    :hoverable="true"
    @click="handleCardClick"
  >
    <!-- 房源图片 -->
    <template #cover>
      <div class="listing-image">
     
        <img
          :src="listing.imageUrl "
          :alt="listing.title"
      
        />
        <div class="listing-source">
          <a-tag :color="sourceColor">{{ sourceText }}</a-tag>
        </div>
      </div>
    </template>

    <!-- 房源信息 -->
    <div class="listing-content">
      <!-- 标题 -->
      <h3 class="listing-title" :title="listing.title">
        {{ listing.title }}
      </h3>

      <!-- 位置信息 -->
      <div class="listing-location">
        <a-space>
          <icon-location class="location-icon" />
          <span class="district">{{ listing.district }}</span>
          <span class="community">{{ listing.community }}</span>
        </a-space>
      </div>

      <!-- 房屋信息 -->
      <div class="listing-info">
        <a-space wrap>
          <a-tag>{{ listing.layout }}</a-tag>
          <a-tag>{{ listing.area }}㎡</a-tag>
          <a-tag v-if="listing.floor">{{ listing.floor }}</a-tag>
          <a-tag v-if="listing.orientation">{{ listing.orientation }}</a-tag>
          <a-tag v-if="listing.buildYear">{{ listing.buildYear }}年</a-tag>
        </a-space>
      </div>

      <!-- 价格信息 -->
      <div class="listing-price">
        <div class="total-price">
          <span class="price-value">{{ listing.totalPrice }}</span>
          <span class="price-unit">万</span>
        </div>
        <div class="unit-price">
          {{ formatUnitPrice(listing.unitPrice) }}元/㎡
        </div>
      </div>

      <!-- 时间信息 -->
      <div class="listing-time">
        <a-space>
          <span class="time-label">更新时间:</span>
          <span class="time-value">{{ formatTime(listing.lastScrapedAt) }}</span>
        </a-space>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #actions>
      <a-space>
        <a-button
          type="text"
          size="small"
          @click.stop="handleViewDetail"
        >
          <template #icon>
            <icon-eye />
          </template>
          查看详情
        </a-button>
        <a-button
          type="text"
          size="small"
          @click.stop="handleSubscribe"
        >
          <template #icon>
            <icon-heart />
          </template>
          关注
        </a-button>
        <a-button
          type="text"
          size="small"
          @click.stop="handleOpenLink"
        >
          <template #icon>
            <icon-link />
          </template>
          原链接
        </a-button>
      </a-space>
    </template>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import {
  IconLocation,
  IconEye,
  IconHeart,
  IconLink
} from '@arco-design/web-vue/es/icon';
import type { HousingListing } from '../api/client';
import { useSubscriptionsStore } from '../store/subscriptions';

interface Props {
  listing: HousingListing;
}

const props = defineProps<Props>();
const router = useRouter();
const subscriptionsStore = useSubscriptionsStore();

// 计算属性
const sourceColor = computed(() => {
  switch (props.listing.source) {
    case 'beike':
      return 'green';
    case 'anjuke':
      return 'blue';
    default:
      return 'gray';
  }
});

const sourceText = computed(() => {
  switch (props.listing.source) {
    case 'beike':
      return '贝壳';
    case 'anjuke':
      return '安居客';
    default:
      return props.listing.source;
  }
});

// 格式化单价
const formatUnitPrice = (price: number): string => {
  return price.toLocaleString();
};

// 格式化时间
const formatTime = (timeStr: string): string => {
  const date = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return date.toLocaleDateString();
  }
};

// 事件处理
const handleCardClick = () => {
  handleViewDetail();
};

const handleViewDetail = () => {
  router.push(`/listings/${props.listing.id}`);
};

const handleSubscribe = async () => {
  try {
    await subscriptionsStore.createSubscription({
      subscriptionType: 'PRICE_CHANGE',
      listingId: props.listing.id
    });
    Message.success('已关注该房源的价格变动');
  } catch (error) {
    Message.error('关注失败，请重试');
  }
};

const handleOpenLink = () => {
  window.open(props.listing.link, '_blank');
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  // img.src = '/placeholder-house.jpg';
};
</script>

<style scoped>
.listing-card {
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.listing-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.listing-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.listing-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.listing-card:hover .listing-image img {
  transform: scale(1.05);
}

.listing-source {
  position: absolute;
  top: 8px;
  right: 8px;
}

.listing-content {
  padding: 16px 0;
}

.listing-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #1d2129;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.listing-location {
  margin-bottom: 12px;
  color: #86909c;
}

.location-icon {
  color: #165dff;
}

.district {
  font-weight: 500;
  color: #4e5969;
}

.community {
  color: #86909c;
}

.listing-info {
  margin-bottom: 16px;
}

.listing-price {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  margin-bottom: 12px;
}

.total-price {
  display: flex;
  align-items: baseline;
}

.price-value {
  font-size: 24px;
  font-weight: 700;
  color: #f53f3f;
}

.price-unit {
  font-size: 14px;
  color: #f53f3f;
  margin-left: 4px;
}

.unit-price {
  font-size: 14px;
  color: #86909c;
}

.listing-time {
  font-size: 12px;
  color: #c9cdd4;
}

.time-label {
  color: #86909c;
}

:deep(.arco-card-actions) {
  border-top: 1px solid #f2f3f5;
  padding: 12px 16px;
}

:deep(.arco-card-actions .arco-btn) {
  color: #86909c;
}

:deep(.arco-card-actions .arco-btn:hover) {
  color: #165dff;
}
</style>
