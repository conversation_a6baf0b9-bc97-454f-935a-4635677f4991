<template>
  <div class="analysis-page">
    <div class="page-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">数据分析</h1>
        <div class="header-actions">
          <a-button @click="handleRefresh" :loading="loading">
            <template #icon>
              <icon-refresh />
            </template>
            刷新数据
          </a-button>
        </div>
      </div>

      <!-- 统计概览 -->
      <a-row :gutter="24" class="stats-overview">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="总房源数"
              :value="stats?.totalListings || 0"
              :value-style="{ color: '#165dff' }"
            >
              <template #prefix>
                <icon-home />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="平均总价"
              :value="stats?.avgTotalPrice || 0"
              :precision="1"
              suffix="万"
              :value-style="{ color: '#f53f3f' }"
            >
              <template #prefix>
                <icon-tag />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="平均单价"
              :value="stats?.avgUnitPrice || 0"
              :precision="0"
              suffix="元/㎡"
              :value-style="{ color: '#00b42a' }"
            >
              <template #prefix>
                <icon-calculator />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="价格区间"
              :value="`${stats?.minPrice || 0} - ${stats?.maxPrice || 0}`"
              suffix="万"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <icon-bar-chart />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>

      <!-- 图表区域 -->
      <a-row :gutter="24" class="charts-row">
        <!-- 区域均价分析 -->
        <a-col :span="12">
          <a-card title="区域均价分析" class="chart-card">
            <div class="chart-container">
              <district-price-chart :district-stats="districtStats" />
            </div>
          </a-card>
        </a-col>

        <!-- 数据来源分布 -->
        <a-col :span="12">
          <a-card title="数据来源分布" class="chart-card">
            <div class="chart-container">
              <source-distribution-chart :source-stats="stats?.sourceStats || []" />
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="24" class="charts-row">
        <!-- 价格趋势分析 -->
        <a-col :span="24">
          <a-card title="价格趋势分析" class="chart-card">
            <template #extra>
              <a-select
                v-model="trendDays"
                style="width: 120px"
                @change="handleTrendDaysChange"
              >
                <a-option :value="7">近7天</a-option>
                <a-option :value="30">近30天</a-option>
                <a-option :value="90">近90天</a-option>
              </a-select>
            </template>
            <div class="chart-container large">
              <price-trend-chart :trend-data="priceTrend" />
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 区域详细统计表格 -->
      <a-card title="区域详细统计" class="table-card">
        <a-table
          :data="districtStats"
          :pagination="false"
          :scroll="{ y: 400 }"
        >
          <template #columns>
            <a-table-column
              title="区域"
              data-index="district"
              :width="120"
              :sortable="{ sortDirections: ['ascend', 'descend'] }"
            />
            <a-table-column
              title="房源数量"
              data-index="count"
              :width="100"
              :sortable="{ sortDirections: ['ascend', 'descend'] }"
            >
              <template #cell="{ record }">
                {{ record.count }} 套
              </template>
            </a-table-column>
            <a-table-column
              title="平均总价"
              data-index="avgTotalPrice"
              :width="120"
              :sortable="{ sortDirections: ['ascend', 'descend'] }"
            >
              <template #cell="{ record }">
                {{ record.avgTotalPrice?.toFixed(1) || 0 }}万
              </template>
            </a-table-column>
            <a-table-column
              title="平均单价"
              data-index="avgUnitPrice"
              :width="120"
              :sortable="{ sortDirections: ['ascend', 'descend'] }"
            >
              <template #cell="{ record }">
                {{ record.avgUnitPrice?.toLocaleString() || 0 }}元/㎡
              </template>
            </a-table-column>
            <a-table-column
              title="操作"
              :width="100"
            >
              <template #cell="{ record }">
                <a-button
                  type="text"
                  size="small"
                  @click="viewDistrictListings(record.district)"
                >
                  查看房源
                </a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import {
  IconRefresh,
  IconHome,
  IconTag,

  IconBarChart
} from '@arco-design/web-vue/es/icon';
import { useListingsStore } from '../store/listings';
import { listingApi } from '../api/client';
import DistrictPriceChart from '../components/DistrictPriceChart.vue';
import SourceDistributionChart from '../components/SourceDistributionChart.vue';
import PriceTrendChart from '../components/PriceTrendChart.vue';

const router = useRouter();
const listingsStore = useListingsStore();

// 状态
const loading = ref(false);
const trendDays = ref(30);
const priceTrend = ref<Array<{
  date: string;
  avgPrice: number;
  count: number;
}>>([]);

// 计算属性
const stats = computed(() => listingsStore.stats);
const districtStats = computed(() => listingsStore.districtStats);

// 事件处理
const handleRefresh = async () => {
  loading.value = true;
  try {
    await Promise.all([
      listingsStore.fetchStats(),
      loadPriceTrend()
    ]);
    Message.success('数据已更新');
  } catch (error) {
    Message.error('数据更新失败');
  } finally {
    loading.value = false;
  }
};

const handleTrendDaysChange = () => {
  loadPriceTrend();
};

const viewDistrictListings = (district: string) => {
  router.push({
    path: '/listings',
    query: { district }
  });
};

// 加载价格趋势数据
const loadPriceTrend = async () => {
  try {
    const data = await listingApi.getPriceTrend(trendDays.value);
    priceTrend.value = data;
  } catch (error) {
    console.error('Error loading price trend:', error);
  }
};

// 页面挂载时加载数据
onMounted(async () => {
  loading.value = true;
  try {
    await Promise.all([
      listingsStore.fetchStats(),
      loadPriceTrend()
    ]);
  } catch (error) {
    console.error('Error loading analysis data:', error);
    Message.error('数据加载失败');
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.analysis-page {
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
  padding: 24px 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
  margin: 0;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-card,
.table-card {
  margin-bottom: 24px;
}

.chart-container {
  height: 300px;
}

.chart-container.large {
  height: 400px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .charts-row .arco-col {
    margin-bottom: 16px;
  }
}
</style>
