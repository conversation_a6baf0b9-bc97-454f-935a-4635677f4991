<template>
  <a-layout-header class="navbar">
    <div class="navbar-content">
      <!-- Logo和标题 -->
      <div class="navbar-brand">
        <router-link to="/" class="brand-link">
          <a-space>
            <icon-home class="brand-icon" />
            <span class="brand-text">房产信息聚合平台</span>
          </a-space>
        </router-link>
      </div>

      <!-- 导航菜单 -->
      <a-menu
        mode="horizontal"
        :selected-keys="selectedKeys"
        class="navbar-menu"
        @menu-item-click="handleMenuClick"
      >
        <a-menu-item key="home">
          <template #icon>
            <icon-dashboard />
          </template>
          首页
        </a-menu-item>
        <a-menu-item key="listings">
          <template #icon>
            <icon-list />
          </template>
          房源列表
        </a-menu-item>
        <a-menu-item key="subscriptions">
          <template #icon>
            <icon-notification />
          </template>
          订阅管理
        </a-menu-item>
        <a-menu-item key="analysis">
          <template #icon>
            <icon-bar-chart />
          </template>
          数据分析
        </a-menu-item>
      </a-menu>

      <!-- 右侧操作区 -->
      <div class="navbar-actions">
        <a-space>
          <!-- 刷新按钮 -->
          <a-tooltip content="刷新数据">
            <a-button
              type="text"
              size="large"
              @click="handleRefresh"
              :loading="refreshLoading"
            >
              <template #icon>
                <icon-refresh />
              </template>
            </a-button>
          </a-tooltip>

          <!-- 手动爬虫按钮 -->
          <a-tooltip content="手动运行爬虫">
            <a-button
              type="text"
              size="large"
              @click="handleRunScraper"
              :loading="scraperLoading"
            >
              <template #icon>
                <icon-robot />
              </template>
            </a-button>
          </a-tooltip>

          <!-- 设置按钮 -->
          <a-tooltip content="设置">
            <a-button type="text" size="large">
              <template #icon>
                <icon-settings />
              </template>
            </a-button>
          </a-tooltip>
        </a-space>
      </div>
    </div>
  </a-layout-header>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import {
  IconHome,
  IconDashboard,
  IconList,
  IconNotification,
  IconBarChart,
  IconRefresh,
  IconRobot,
  IconSettings
} from '@arco-design/web-vue/es/icon';
import { scraperApi } from '../api/client';

const router = useRouter();
const route = useRoute();

// 状态
const refreshLoading = ref(false);
const scraperLoading = ref(false);

// 计算当前选中的菜单项
const selectedKeys = computed(() => {
  const routeName = route.name as string;
  switch (routeName) {
    case 'Home':
      return ['home'];
    case 'ListingList':
      return ['listings'];
    case 'Subscriptions':
      return ['subscriptions'];
    case 'Analysis':
      return ['analysis'];
    default:
      return [];
  }
});

// 菜单点击处理
const handleMenuClick = (key: string) => {
  switch (key) {
    case 'home':
      router.push('/');
      break;
    case 'listings':
      router.push('/listings');
      break;
    case 'subscriptions':
      router.push('/subscriptions');
      break;
    case 'analysis':
      router.push('/analysis');
      break;
  }
};

// 刷新数据
const handleRefresh = () => {
  refreshLoading.value = true;
  // 触发当前页面的数据刷新
  window.location.reload();
};

// 手动运行爬虫
const handleRunScraper = async () => {
  try {
    scraperLoading.value = true;
    const result = await scraperApi.runScraper({ pages: 1 });
    
    if (result.success) {
      Message.success('爬虫任务已启动，请稍后查看数据更新');
    } else {
      Message.error(result.message || '爬虫任务启动失败');
    }
  } catch (error) {
    console.error('Error running scraper:', error);
    Message.error('爬虫任务启动失败');
  } finally {
    scraperLoading.value = false;
  }
};
</script>

<style scoped>
.navbar {
  background: #fff;
  border-bottom: 1px solid #e5e6eb;
  padding: 0;
  height: 64px;
  line-height: 64px;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.navbar-brand {
  flex-shrink: 0;
}

.brand-link {
  color: #1d2129;
  text-decoration: none;
  font-weight: 600;
  font-size: 18px;
}

.brand-link:hover {
  color: #165dff;
}

.brand-icon {
  font-size: 24px;
  color: #165dff;
}

.brand-text {
  font-size: 18px;
}

.navbar-menu {
  flex: 1;
  border-bottom: none;
  background: transparent;
  margin: 0 40px;
}

.navbar-actions {
  flex-shrink: 0;
}

:deep(.arco-menu-horizontal .arco-menu-item) {
  border-bottom: 2px solid transparent;
}

:deep(.arco-menu-horizontal .arco-menu-item:hover) {
  border-bottom-color: #165dff;
}

:deep(.arco-menu-horizontal .arco-menu-item.arco-menu-selected) {
  border-bottom-color: #165dff;
  color: #165dff;
}
</style>
