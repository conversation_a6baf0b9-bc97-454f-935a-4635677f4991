{"version": 3, "file": "createSeriesData-CKwrkLIZ.js", "names": ["resultItem", "SeriesDimensionDefine", "coordDim", "resultDimIdx", "i", "map", "CoordSysInfo", "SeriesData"], "sources": ["../../.pnpm/echarts@5.6.0/node_modules/echarts/lib/data/helper/createDimensions.js", "../../.pnpm/echarts@5.6.0/node_modules/echarts/lib/model/referHelper.js", "../../.pnpm/echarts@5.6.0/node_modules/echarts/lib/chart/helper/createSeriesData.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { VISUAL_DIMENSIONS } from '../../util/types.js';\nimport SeriesDimensionDefine from '../SeriesDimensionDefine.js';\nimport { createHashMap, defaults, each, extend, isObject, isString } from 'zrender/lib/core/util.js';\nimport { createSourceFromSeriesDataOption, isSourceInstance } from '../Source.js';\nimport { CtorInt32Array } from '../DataStore.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { BE_ORDINAL, guessOrdinal } from './sourceHelper.js';\nimport { createDimNameMap, ensureSourceDimNameMap, SeriesDataSchema, shouldOmitUnusedDimensions } from './SeriesDataSchema.js';\n/**\r\n * For outside usage compat (like echarts-gl are using it).\r\n */\nexport function createDimensions(source, opt) {\n  return prepareSeriesDataSchema(source, opt).dimensions;\n}\n/**\r\n * This method builds the relationship between:\r\n * + \"what the coord sys or series requires (see `coordDimensions`)\",\r\n * + \"what the user defines (in `encode` and `dimensions`, see `opt.dimensionsDefine` and `opt.encodeDefine`)\"\r\n * + \"what the data source provids (see `source`)\".\r\n *\r\n * Some guess strategy will be adapted if user does not define something.\r\n * If no 'value' dimension specified, the first no-named dimension will be\r\n * named as 'value'.\r\n *\r\n * @return The results are always sorted by `storeDimIndex` asc.\r\n */\nexport default function prepareSeriesDataSchema(\n// TODO: TYPE completeDimensions type\nsource, opt) {\n  if (!isSourceInstance(source)) {\n    source = createSourceFromSeriesDataOption(source);\n  }\n  opt = opt || {};\n  var sysDims = opt.coordDimensions || [];\n  var dimsDef = opt.dimensionsDefine || source.dimensionsDefine || [];\n  var coordDimNameMap = createHashMap();\n  var resultList = [];\n  var dimCount = getDimCount(source, sysDims, dimsDef, opt.dimensionsCount);\n  // Try to ignore unused dimensions if sharing a high dimension datastore\n  // 30 is an experience value.\n  var omitUnusedDimensions = opt.canOmitUnusedDimensions && shouldOmitUnusedDimensions(dimCount);\n  var isUsingSourceDimensionsDef = dimsDef === source.dimensionsDefine;\n  var dataDimNameMap = isUsingSourceDimensionsDef ? ensureSourceDimNameMap(source) : createDimNameMap(dimsDef);\n  var encodeDef = opt.encodeDefine;\n  if (!encodeDef && opt.encodeDefaulter) {\n    encodeDef = opt.encodeDefaulter(source, dimCount);\n  }\n  var encodeDefMap = createHashMap(encodeDef);\n  var indicesMap = new CtorInt32Array(dimCount);\n  for (var i = 0; i < indicesMap.length; i++) {\n    indicesMap[i] = -1;\n  }\n  function getResultItem(dimIdx) {\n    var idx = indicesMap[dimIdx];\n    if (idx < 0) {\n      var dimDefItemRaw = dimsDef[dimIdx];\n      var dimDefItem = isObject(dimDefItemRaw) ? dimDefItemRaw : {\n        name: dimDefItemRaw\n      };\n      var resultItem = new SeriesDimensionDefine();\n      var userDimName = dimDefItem.name;\n      if (userDimName != null && dataDimNameMap.get(userDimName) != null) {\n        // Only if `series.dimensions` is defined in option\n        // displayName, will be set, and dimension will be displayed vertically in\n        // tooltip by default.\n        resultItem.name = resultItem.displayName = userDimName;\n      }\n      dimDefItem.type != null && (resultItem.type = dimDefItem.type);\n      dimDefItem.displayName != null && (resultItem.displayName = dimDefItem.displayName);\n      var newIdx = resultList.length;\n      indicesMap[dimIdx] = newIdx;\n      resultItem.storeDimIndex = dimIdx;\n      resultList.push(resultItem);\n      return resultItem;\n    }\n    return resultList[idx];\n  }\n  if (!omitUnusedDimensions) {\n    for (var i = 0; i < dimCount; i++) {\n      getResultItem(i);\n    }\n  }\n  // Set `coordDim` and `coordDimIndex` by `encodeDefMap` and normalize `encodeDefMap`.\n  encodeDefMap.each(function (dataDimsRaw, coordDim) {\n    var dataDims = normalizeToArray(dataDimsRaw).slice();\n    // Note: It is allowed that `dataDims.length` is `0`, e.g., options is\n    // `{encode: {x: -1, y: 1}}`. Should not filter anything in\n    // this case.\n    if (dataDims.length === 1 && !isString(dataDims[0]) && dataDims[0] < 0) {\n      encodeDefMap.set(coordDim, false);\n      return;\n    }\n    var validDataDims = encodeDefMap.set(coordDim, []);\n    each(dataDims, function (resultDimIdxOrName, idx) {\n      // The input resultDimIdx can be dim name or index.\n      var resultDimIdx = isString(resultDimIdxOrName) ? dataDimNameMap.get(resultDimIdxOrName) : resultDimIdxOrName;\n      if (resultDimIdx != null && resultDimIdx < dimCount) {\n        validDataDims[idx] = resultDimIdx;\n        applyDim(getResultItem(resultDimIdx), coordDim, idx);\n      }\n    });\n  });\n  // Apply templates and default order from `sysDims`.\n  var availDimIdx = 0;\n  each(sysDims, function (sysDimItemRaw) {\n    var coordDim;\n    var sysDimItemDimsDef;\n    var sysDimItemOtherDims;\n    var sysDimItem;\n    if (isString(sysDimItemRaw)) {\n      coordDim = sysDimItemRaw;\n      sysDimItem = {};\n    } else {\n      sysDimItem = sysDimItemRaw;\n      coordDim = sysDimItem.name;\n      var ordinalMeta = sysDimItem.ordinalMeta;\n      sysDimItem.ordinalMeta = null;\n      sysDimItem = extend({}, sysDimItem);\n      sysDimItem.ordinalMeta = ordinalMeta;\n      // `coordDimIndex` should not be set directly.\n      sysDimItemDimsDef = sysDimItem.dimsDef;\n      sysDimItemOtherDims = sysDimItem.otherDims;\n      sysDimItem.name = sysDimItem.coordDim = sysDimItem.coordDimIndex = sysDimItem.dimsDef = sysDimItem.otherDims = null;\n    }\n    var dataDims = encodeDefMap.get(coordDim);\n    // negative resultDimIdx means no need to mapping.\n    if (dataDims === false) {\n      return;\n    }\n    dataDims = normalizeToArray(dataDims);\n    // dimensions provides default dim sequences.\n    if (!dataDims.length) {\n      for (var i = 0; i < (sysDimItemDimsDef && sysDimItemDimsDef.length || 1); i++) {\n        while (availDimIdx < dimCount && getResultItem(availDimIdx).coordDim != null) {\n          availDimIdx++;\n        }\n        availDimIdx < dimCount && dataDims.push(availDimIdx++);\n      }\n    }\n    // Apply templates.\n    each(dataDims, function (resultDimIdx, coordDimIndex) {\n      var resultItem = getResultItem(resultDimIdx);\n      // Coordinate system has a higher priority on dim type than source.\n      if (isUsingSourceDimensionsDef && sysDimItem.type != null) {\n        resultItem.type = sysDimItem.type;\n      }\n      applyDim(defaults(resultItem, sysDimItem), coordDim, coordDimIndex);\n      if (resultItem.name == null && sysDimItemDimsDef) {\n        var sysDimItemDimsDefItem = sysDimItemDimsDef[coordDimIndex];\n        !isObject(sysDimItemDimsDefItem) && (sysDimItemDimsDefItem = {\n          name: sysDimItemDimsDefItem\n        });\n        resultItem.name = resultItem.displayName = sysDimItemDimsDefItem.name;\n        resultItem.defaultTooltip = sysDimItemDimsDefItem.defaultTooltip;\n      }\n      // FIXME refactor, currently only used in case: {otherDims: {tooltip: false}}\n      sysDimItemOtherDims && defaults(resultItem.otherDims, sysDimItemOtherDims);\n    });\n  });\n  function applyDim(resultItem, coordDim, coordDimIndex) {\n    if (VISUAL_DIMENSIONS.get(coordDim) != null) {\n      resultItem.otherDims[coordDim] = coordDimIndex;\n    } else {\n      resultItem.coordDim = coordDim;\n      resultItem.coordDimIndex = coordDimIndex;\n      coordDimNameMap.set(coordDim, true);\n    }\n  }\n  // Make sure the first extra dim is 'value'.\n  var generateCoord = opt.generateCoord;\n  var generateCoordCount = opt.generateCoordCount;\n  var fromZero = generateCoordCount != null;\n  generateCoordCount = generateCoord ? generateCoordCount || 1 : 0;\n  var extra = generateCoord || 'value';\n  function ifNoNameFillWithCoordName(resultItem) {\n    if (resultItem.name == null) {\n      // Duplication will be removed in the next step.\n      resultItem.name = resultItem.coordDim;\n    }\n  }\n  // Set dim `name` and other `coordDim` and other props.\n  if (!omitUnusedDimensions) {\n    for (var resultDimIdx = 0; resultDimIdx < dimCount; resultDimIdx++) {\n      var resultItem = getResultItem(resultDimIdx);\n      var coordDim = resultItem.coordDim;\n      if (coordDim == null) {\n        // TODO no need to generate coordDim for isExtraCoord?\n        resultItem.coordDim = genCoordDimName(extra, coordDimNameMap, fromZero);\n        resultItem.coordDimIndex = 0;\n        // Series specified generateCoord is using out.\n        if (!generateCoord || generateCoordCount <= 0) {\n          resultItem.isExtraCoord = true;\n        }\n        generateCoordCount--;\n      }\n      ifNoNameFillWithCoordName(resultItem);\n      if (resultItem.type == null && (guessOrdinal(source, resultDimIdx) === BE_ORDINAL.Must\n      // Consider the case:\n      // {\n      //    dataset: {source: [\n      //        ['2001', 123],\n      //        ['2002', 456],\n      //        ...\n      //        ['The others', 987],\n      //    ]},\n      //    series: {type: 'pie'}\n      // }\n      // The first column should better be treated as a \"ordinal\" although it\n      // might not be detected as an \"ordinal\" by `guessOrdinal`.\n      || resultItem.isExtraCoord && (resultItem.otherDims.itemName != null || resultItem.otherDims.seriesName != null))) {\n        resultItem.type = 'ordinal';\n      }\n    }\n  } else {\n    each(resultList, function (resultItem) {\n      // PENDING: guessOrdinal or let user specify type: 'ordinal' manually?\n      ifNoNameFillWithCoordName(resultItem);\n    });\n    // Sort dimensions: there are some rule that use the last dim as label,\n    // and for some latter travel process easier.\n    resultList.sort(function (item0, item1) {\n      return item0.storeDimIndex - item1.storeDimIndex;\n    });\n  }\n  removeDuplication(resultList);\n  return new SeriesDataSchema({\n    source: source,\n    dimensions: resultList,\n    fullDimensionCount: dimCount,\n    dimensionOmitted: omitUnusedDimensions\n  });\n}\nfunction removeDuplication(result) {\n  var duplicationMap = createHashMap();\n  for (var i = 0; i < result.length; i++) {\n    var dim = result[i];\n    var dimOriginalName = dim.name;\n    var count = duplicationMap.get(dimOriginalName) || 0;\n    if (count > 0) {\n      // Starts from 0.\n      dim.name = dimOriginalName + (count - 1);\n    }\n    count++;\n    duplicationMap.set(dimOriginalName, count);\n  }\n}\n// ??? TODO\n// Originally detect dimCount by data[0]. Should we\n// optimize it to only by sysDims and dimensions and encode.\n// So only necessary dims will be initialized.\n// But\n// (1) custom series should be considered. where other dims\n// may be visited.\n// (2) sometimes user need to calculate bubble size or use visualMap\n// on other dimensions besides coordSys needed.\n// So, dims that is not used by system, should be shared in data store?\nfunction getDimCount(source, sysDims, dimsDef, optDimCount) {\n  // Note that the result dimCount should not small than columns count\n  // of data, otherwise `dataDimNameMap` checking will be incorrect.\n  var dimCount = Math.max(source.dimensionsDetectedCount || 1, sysDims.length, dimsDef.length, optDimCount || 0);\n  each(sysDims, function (sysDimItem) {\n    var sysDimItemDimsDef;\n    if (isObject(sysDimItem) && (sysDimItemDimsDef = sysDimItem.dimsDef)) {\n      dimCount = Math.max(dimCount, sysDimItemDimsDef.length);\n    }\n  });\n  return dimCount;\n}\nfunction genCoordDimName(name, map, fromZero) {\n  if (fromZero || map.hasKey(name)) {\n    var i = 0;\n    while (map.hasKey(name + i)) {\n      i++;\n    }\n    name += i;\n  }\n  map.set(name, true);\n  return name;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Helper for model references.\r\n * There are many manners to refer axis/coordSys.\r\n */\n// TODO\n// merge relevant logic to this file?\n// check: \"modelHelper\" of tooltip and \"BrushTargetManager\".\nimport { createHashMap, retrieve, each } from 'zrender/lib/core/util.js';\nimport { SINGLE_REFERRING } from '../util/model.js';\n/**\r\n * @class\r\n * For example:\r\n * {\r\n *     coordSysName: 'cartesian2d',\r\n *     coordSysDims: ['x', 'y', ...],\r\n *     axisMap: HashMap({\r\n *         x: xAxisModel,\r\n *         y: yAxisModel\r\n *     }),\r\n *     categoryAxisMap: HashMap({\r\n *         x: xAxisModel,\r\n *         y: undefined\r\n *     }),\r\n *     // The index of the first category axis in `coordSysDims`.\r\n *     // `null/undefined` means no category axis exists.\r\n *     firstCategoryDimIndex: 1,\r\n *     // To replace user specified encode.\r\n * }\r\n */\nvar CoordSysInfo = /** @class */function () {\n  function CoordSysInfo(coordSysName) {\n    this.coordSysDims = [];\n    this.axisMap = createHashMap();\n    this.categoryAxisMap = createHashMap();\n    this.coordSysName = coordSysName;\n  }\n  return CoordSysInfo;\n}();\nexport function getCoordSysInfoBySeries(seriesModel) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var result = new CoordSysInfo(coordSysName);\n  var fetch = fetchers[coordSysName];\n  if (fetch) {\n    fetch(seriesModel, result, result.axisMap, result.categoryAxisMap);\n    return result;\n  }\n}\nvar fetchers = {\n  cartesian2d: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var xAxisModel = seriesModel.getReferringComponents('xAxis', SINGLE_REFERRING).models[0];\n    var yAxisModel = seriesModel.getReferringComponents('yAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!xAxisModel) {\n        throw new Error('xAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('xAxisId'), 0) + '\" not found');\n      }\n      if (!yAxisModel) {\n        throw new Error('yAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('yAxisId'), 0) + '\" not found');\n      }\n    }\n    result.coordSysDims = ['x', 'y'];\n    axisMap.set('x', xAxisModel);\n    axisMap.set('y', yAxisModel);\n    if (isCategory(xAxisModel)) {\n      categoryAxisMap.set('x', xAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(yAxisModel)) {\n      categoryAxisMap.set('y', yAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  singleAxis: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var singleAxisModel = seriesModel.getReferringComponents('singleAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!singleAxisModel) {\n        throw new Error('singleAxis should be specified.');\n      }\n    }\n    result.coordSysDims = ['single'];\n    axisMap.set('single', singleAxisModel);\n    if (isCategory(singleAxisModel)) {\n      categoryAxisMap.set('single', singleAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n  },\n  polar: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var polarModel = seriesModel.getReferringComponents('polar', SINGLE_REFERRING).models[0];\n    var radiusAxisModel = polarModel.findAxisModel('radiusAxis');\n    var angleAxisModel = polarModel.findAxisModel('angleAxis');\n    if (process.env.NODE_ENV !== 'production') {\n      if (!angleAxisModel) {\n        throw new Error('angleAxis option not found');\n      }\n      if (!radiusAxisModel) {\n        throw new Error('radiusAxis option not found');\n      }\n    }\n    result.coordSysDims = ['radius', 'angle'];\n    axisMap.set('radius', radiusAxisModel);\n    axisMap.set('angle', angleAxisModel);\n    if (isCategory(radiusAxisModel)) {\n      categoryAxisMap.set('radius', radiusAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(angleAxisModel)) {\n      categoryAxisMap.set('angle', angleAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  geo: function (seriesModel, result, axisMap, categoryAxisMap) {\n    result.coordSysDims = ['lng', 'lat'];\n  },\n  parallel: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var ecModel = seriesModel.ecModel;\n    var parallelModel = ecModel.getComponent('parallel', seriesModel.get('parallelIndex'));\n    var coordSysDims = result.coordSysDims = parallelModel.dimensions.slice();\n    each(parallelModel.parallelAxisIndex, function (axisIndex, index) {\n      var axisModel = ecModel.getComponent('parallelAxis', axisIndex);\n      var axisDim = coordSysDims[index];\n      axisMap.set(axisDim, axisModel);\n      if (isCategory(axisModel)) {\n        categoryAxisMap.set(axisDim, axisModel);\n        if (result.firstCategoryDimIndex == null) {\n          result.firstCategoryDimIndex = index;\n        }\n      }\n    });\n  }\n};\nfunction isCategory(axisModel) {\n  return axisModel.get('type') === 'category';\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport { getDataItemValue } from '../../util/model.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport { getCoordSysInfoBySeries } from '../../model/referHelper.js';\nimport { createSourceFromSeriesDataOption } from '../../data/Source.js';\nimport { enableDataStack } from '../../data/helper/dataStackHelper.js';\nimport { makeSeriesEncodeForAxisCoordSys } from '../../data/helper/sourceHelper.js';\nimport { SOURCE_FORMAT_ORIGINAL } from '../../util/types.js';\nfunction getCoordSysDimDefs(seriesModel, coordSysInfo) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var registeredCoordSys = CoordinateSystem.get(coordSysName);\n  var coordSysDimDefs;\n  if (coordSysInfo && coordSysInfo.coordSysDims) {\n    coordSysDimDefs = zrUtil.map(coordSysInfo.coordSysDims, function (dim) {\n      var dimInfo = {\n        name: dim\n      };\n      var axisModel = coordSysInfo.axisMap.get(dim);\n      if (axisModel) {\n        var axisType = axisModel.get('type');\n        dimInfo.type = getDimensionTypeByAxis(axisType);\n      }\n      return dimInfo;\n    });\n  }\n  if (!coordSysDimDefs) {\n    // Get dimensions from registered coordinate system\n    coordSysDimDefs = registeredCoordSys && (registeredCoordSys.getDimensionsInfo ? registeredCoordSys.getDimensionsInfo() : registeredCoordSys.dimensions.slice()) || ['x', 'y'];\n  }\n  return coordSysDimDefs;\n}\nfunction injectOrdinalMeta(dimInfoList, createInvertedIndices, coordSysInfo) {\n  var firstCategoryDimIndex;\n  var hasNameEncode;\n  coordSysInfo && zrUtil.each(dimInfoList, function (dimInfo, dimIndex) {\n    var coordDim = dimInfo.coordDim;\n    var categoryAxisModel = coordSysInfo.categoryAxisMap.get(coordDim);\n    if (categoryAxisModel) {\n      if (firstCategoryDimIndex == null) {\n        firstCategoryDimIndex = dimIndex;\n      }\n      dimInfo.ordinalMeta = categoryAxisModel.getOrdinalMeta();\n      if (createInvertedIndices) {\n        dimInfo.createInvertedIndices = true;\n      }\n    }\n    if (dimInfo.otherDims.itemName != null) {\n      hasNameEncode = true;\n    }\n  });\n  if (!hasNameEncode && firstCategoryDimIndex != null) {\n    dimInfoList[firstCategoryDimIndex].otherDims.itemName = 0;\n  }\n  return firstCategoryDimIndex;\n}\n/**\r\n * Caution: there are side effects to `sourceManager` in this method.\r\n * Should better only be called in `Series['getInitialData']`.\r\n */\nfunction createSeriesData(sourceRaw, seriesModel, opt) {\n  opt = opt || {};\n  var sourceManager = seriesModel.getSourceManager();\n  var source;\n  var isOriginalSource = false;\n  if (sourceRaw) {\n    isOriginalSource = true;\n    source = createSourceFromSeriesDataOption(sourceRaw);\n  } else {\n    source = sourceManager.getSource();\n    // Is series.data. not dataset.\n    isOriginalSource = source.sourceFormat === SOURCE_FORMAT_ORIGINAL;\n  }\n  var coordSysInfo = getCoordSysInfoBySeries(seriesModel);\n  var coordSysDimDefs = getCoordSysDimDefs(seriesModel, coordSysInfo);\n  var useEncodeDefaulter = opt.useEncodeDefaulter;\n  var encodeDefaulter = zrUtil.isFunction(useEncodeDefaulter) ? useEncodeDefaulter : useEncodeDefaulter ? zrUtil.curry(makeSeriesEncodeForAxisCoordSys, coordSysDimDefs, seriesModel) : null;\n  var createDimensionOptions = {\n    coordDimensions: coordSysDimDefs,\n    generateCoord: opt.generateCoord,\n    encodeDefine: seriesModel.getEncode(),\n    encodeDefaulter: encodeDefaulter,\n    canOmitUnusedDimensions: !isOriginalSource\n  };\n  var schema = prepareSeriesDataSchema(source, createDimensionOptions);\n  var firstCategoryDimIndex = injectOrdinalMeta(schema.dimensions, opt.createInvertedIndices, coordSysInfo);\n  var store = !isOriginalSource ? sourceManager.getSharedDataStore(schema) : null;\n  var stackCalculationInfo = enableDataStack(seriesModel, {\n    schema: schema,\n    store: store\n  });\n  var data = new SeriesData(schema, seriesModel);\n  data.setCalculationInfo(stackCalculationInfo);\n  var dimValueGetter = firstCategoryDimIndex != null && isNeedCompleteOrdinalData(source) ? function (itemOpt, dimName, dataIndex, dimIndex) {\n    // Use dataIndex as ordinal value in categoryAxis\n    return dimIndex === firstCategoryDimIndex ? dataIndex : this.defaultDimValueGetter(itemOpt, dimName, dataIndex, dimIndex);\n  } : null;\n  data.hasItemOption = false;\n  data.initData(\n  // Try to reuse the data store in sourceManager if using dataset.\n  isOriginalSource ? source : store, null, dimValueGetter);\n  return data;\n}\nfunction isNeedCompleteOrdinalData(source) {\n  if (source.sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var sampleItem = firstDataNotNull(source.data || []);\n    return !zrUtil.isArray(getDataItemValue(sampleItem));\n  }\n}\nfunction firstDataNotNull(arr) {\n  var i = 0;\n  while (i < arr.length && arr[i] == null) {\n    i++;\n  }\n  return arr[i];\n}\nexport default createSeriesData;"], "x_google_ignoreList": [0, 1, 2], "mappings": ";;;;;;;;;AAsDA,SAAgB,iBAAiB,QAAQ,KAAK;AAC5C,QAAO,wBAAwB,QAAQ,IAAI,CAAC;AAC7C;;;;;;;;;;;;;;;;;;;;;;;;AAaD,SAAwB,wBAExB,QAAQ,KAAK;AACX,MAAK,iBAAiB,OAAO,CAC3B,UAAS,iCAAiC,OAAO;AAEnD,OAAM,OAAO,CAAE;CACf,IAAI,UAAU,IAAI,mBAAmB,CAAE;CACvC,IAAI,UAAU,IAAI,oBAAoB,OAAO,oBAAoB,CAAE;CACnE,IAAI,kBAAkB,eAAe;CACrC,IAAI,aAAa,CAAE;CACnB,IAAI,WAAW,YAAY,QAAQ,SAAS,SAAS,IAAI,gBAAgB;CAGzE,IAAI,uBAAuB,IAAI,2BAA2B,2BAA2B,SAAS;CAC9F,IAAI,6BAA6B,YAAY,OAAO;CACpD,IAAI,iBAAiB,6BAA6B,uBAAuB,OAAO,GAAG,iBAAiB,QAAQ;CAC5G,IAAI,YAAY,IAAI;AACpB,MAAK,aAAa,IAAI,gBACpB,aAAY,IAAI,gBAAgB,QAAQ,SAAS;CAEnD,IAAI,eAAe,cAAc,UAAU;CAC3C,IAAI,aAAa,IAAI,eAAe;AACpC,MAAK,IAAI,IAAI,GAAG,IAAI,WAAW,QAAQ,IACrC,YAAW,KAAK;CAElB,SAAS,cAAc,QAAQ;EAC7B,IAAI,MAAM,WAAW;AACrB,MAAI,MAAM,GAAG;GACX,IAAI,gBAAgB,QAAQ;GAC5B,IAAI,aAAa,SAAS,cAAc,GAAG,gBAAgB,EACzD,MAAM,cACP;GACD,IAAIA,eAAa,IAAIC;GACrB,IAAI,cAAc,WAAW;AAC7B,OAAI,eAAe,QAAQ,eAAe,IAAI,YAAY,IAAI,KAI5D,cAAW,OAAOD,aAAW,cAAc;AAE7C,cAAW,QAAQ,SAASA,aAAW,OAAO,WAAW;AACzD,cAAW,eAAe,SAASA,aAAW,cAAc,WAAW;GACvE,IAAI,SAAS,WAAW;AACxB,cAAW,UAAU;AACrB,gBAAW,gBAAgB;AAC3B,cAAW,KAAKA,aAAW;AAC3B,UAAOA;EACR;AACD,SAAO,WAAW;CACnB;AACD,MAAK,qBACH,MAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAC5B,eAAc,EAAE;AAIpB,cAAa,KAAK,SAAU,aAAaE,YAAU;EACjD,IAAI,WAAW,iBAAiB,YAAY,CAAC,OAAO;AAIpD,MAAI,SAAS,WAAW,MAAM,SAAS,SAAS,GAAG,IAAI,SAAS,KAAK,GAAG;AACtE,gBAAa,IAAIA,YAAU,MAAM;AACjC;EACD;EACD,IAAI,gBAAgB,aAAa,IAAIA,YAAU,CAAE,EAAC;AAClD,OAAK,UAAU,SAAU,oBAAoB,KAAK;GAEhD,IAAIC,iBAAe,SAAS,mBAAmB,GAAG,eAAe,IAAI,mBAAmB,GAAG;AAC3F,OAAIA,kBAAgB,QAAQA,iBAAe,UAAU;AACnD,kBAAc,OAAOA;AACrB,aAAS,cAAcA,eAAa,EAAED,YAAU,IAAI;GACrD;EACF,EAAC;CACH,EAAC;CAEF,IAAI,cAAc;AAClB,MAAK,SAAS,SAAU,eAAe;EACrC,IAAIA;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;AACJ,MAAI,SAAS,cAAc,EAAE;AAC3B,gBAAW;AACX,gBAAa,CAAE;EAChB,OAAM;AACL,gBAAa;AACb,gBAAW,WAAW;GACtB,IAAI,cAAc,WAAW;AAC7B,cAAW,cAAc;AACzB,gBAAa,OAAO,CAAE,GAAE,WAAW;AACnC,cAAW,cAAc;AAEzB,uBAAoB,WAAW;AAC/B,yBAAsB,WAAW;AACjC,cAAW,OAAO,WAAW,WAAW,WAAW,gBAAgB,WAAW,UAAU,WAAW,YAAY;EAChH;EACD,IAAI,WAAW,aAAa,IAAIA,WAAS;AAEzC,MAAI,aAAa,MACf;AAEF,aAAW,iBAAiB,SAAS;AAErC,OAAK,SAAS,OACZ,MAAK,IAAIE,MAAI,GAAGA,OAAK,qBAAqB,kBAAkB,UAAU,IAAIA,OAAK;AAC7E,UAAO,cAAc,YAAY,cAAc,YAAY,CAAC,YAAY,KACtE;AAEF,iBAAc,YAAY,SAAS,KAAK,cAAc;EACvD;AAGH,OAAK,UAAU,SAAUD,gBAAc,eAAe;GACpD,IAAIH,eAAa,cAAcG,eAAa;AAE5C,OAAI,8BAA8B,WAAW,QAAQ,KACnD,cAAW,OAAO,WAAW;AAE/B,YAAS,SAASH,cAAY,WAAW,EAAEE,YAAU,cAAc;AACnE,OAAIF,aAAW,QAAQ,QAAQ,mBAAmB;IAChD,IAAI,wBAAwB,kBAAkB;AAC9C,KAAC,SAAS,sBAAsB,KAAK,wBAAwB,EAC3D,MAAM,sBACP;AACD,iBAAW,OAAOA,aAAW,cAAc,sBAAsB;AACjE,iBAAW,iBAAiB,sBAAsB;GACnD;AAED,0BAAuB,SAASA,aAAW,WAAW,oBAAoB;EAC3E,EAAC;CACH,EAAC;CACF,SAAS,SAASA,cAAYE,YAAU,eAAe;AACrD,MAAI,kBAAkB,IAAIA,WAAS,IAAI,KACrC,cAAW,UAAUA,cAAY;OAC5B;AACL,gBAAW,WAAWA;AACtB,gBAAW,gBAAgB;AAC3B,mBAAgB,IAAIA,YAAU,KAAK;EACpC;CACF;CAED,IAAI,gBAAgB,IAAI;CACxB,IAAI,qBAAqB,IAAI;CAC7B,IAAI,WAAW,sBAAsB;AACrC,sBAAqB,gBAAgB,sBAAsB,IAAI;CAC/D,IAAI,QAAQ,iBAAiB;CAC7B,SAAS,0BAA0BF,cAAY;AAC7C,MAAIA,aAAW,QAAQ,KAErB,cAAW,OAAOA,aAAW;CAEhC;AAED,MAAK,qBACH,MAAK,IAAI,eAAe,GAAG,eAAe,UAAU,gBAAgB;EAClE,IAAI,aAAa,cAAc,aAAa;EAC5C,IAAI,WAAW,WAAW;AAC1B,MAAI,YAAY,MAAM;AAEpB,cAAW,WAAW,gBAAgB,OAAO,iBAAiB,SAAS;AACvE,cAAW,gBAAgB;AAE3B,QAAK,iBAAiB,sBAAsB,EAC1C,YAAW,eAAe;AAE5B;EACD;AACD,4BAA0B,WAAW;AACrC,MAAI,WAAW,QAAQ,SAAS,aAAa,QAAQ,aAAa,KAAK,WAAW,QAa/E,WAAW,iBAAiB,WAAW,UAAU,YAAY,QAAQ,WAAW,UAAU,cAAc,OACzG,YAAW,OAAO;CAErB;MACI;AACL,OAAK,YAAY,SAAUA,cAAY;AAErC,6BAA0BA,aAAW;EACtC,EAAC;AAGF,aAAW,KAAK,SAAU,OAAO,OAAO;AACtC,UAAO,MAAM,gBAAgB,MAAM;EACpC,EAAC;CACH;AACD,mBAAkB,WAAW;AAC7B,QAAO,IAAI,iBAAiB;EAClB;EACR,YAAY;EACZ,oBAAoB;EACpB,kBAAkB;CACnB;AACF;AACD,SAAS,kBAAkB,QAAQ;CACjC,IAAI,iBAAiB,eAAe;AACpC,MAAK,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;EACtC,IAAI,MAAM,OAAO;EACjB,IAAI,kBAAkB,IAAI;EAC1B,IAAI,QAAQ,eAAe,IAAI,gBAAgB,IAAI;AACnD,MAAI,QAAQ,EAEV,KAAI,OAAO,mBAAmB,QAAQ;AAExC;AACA,iBAAe,IAAI,iBAAiB,MAAM;CAC3C;AACF;AAWD,SAAS,YAAY,QAAQ,SAAS,SAAS,aAAa;CAG1D,IAAI,WAAW,KAAK,IAAI,OAAO,2BAA2B,GAAG,QAAQ,QAAQ,QAAQ,QAAQ,eAAe,EAAE;AAC9G,MAAK,SAAS,SAAU,YAAY;EAClC,IAAI;AACJ,MAAI,SAAS,WAAW,KAAK,oBAAoB,WAAW,SAC1D,YAAW,KAAK,IAAI,UAAU,kBAAkB,OAAO;CAE1D,EAAC;AACF,QAAO;AACR;AACD,SAAS,gBAAgB,MAAMK,OAAK,UAAU;AAC5C,KAAI,YAAY,MAAI,OAAO,KAAK,EAAE;EAChC,IAAI,IAAI;AACR,SAAO,MAAI,OAAO,OAAO,EAAE,CACzB;AAEF,UAAQ;CACT;AACD,OAAI,IAAI,MAAM,KAAK;AACnB,QAAO;AACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzPD,IAAI,eAA4B,WAAY;CAC1C,SAASC,eAAa,cAAc;AAClC,OAAK,eAAe,CAAE;AACtB,OAAK,UAAU,eAAe;AAC9B,OAAK,kBAAkB,eAAe;AACtC,OAAK,eAAe;CACrB;AACD,QAAOA;AACR,GAAE;AACH,SAAgB,wBAAwB,aAAa;CACnD,IAAI,eAAe,YAAY,IAAI,mBAAmB;CACtD,IAAI,SAAS,IAAI,aAAa;CAC9B,IAAI,QAAQ,SAAS;AACrB,KAAI,OAAO;AACT,QAAM,aAAa,QAAQ,OAAO,SAAS,OAAO,gBAAgB;AAClE,SAAO;CACR;AACF;AACD,IAAI,WAAW;CACb,aAAa,SAAU,aAAa,QAAQ,SAAS,iBAAiB;EACpE,IAAI,aAAa,YAAY,uBAAuB,SAAS,iBAAiB,CAAC,OAAO;EACtF,IAAI,aAAa,YAAY,uBAAuB,SAAS,iBAAiB,CAAC,OAAO;EAC3C;AACzC,QAAK,WACH,OAAM,IAAI,MAAM,aAAY,SAAS,YAAY,IAAI,aAAa,EAAE,YAAY,IAAI,UAAU,EAAE,EAAE,GAAG;AAEvG,QAAK,WACH,OAAM,IAAI,MAAM,aAAY,SAAS,YAAY,IAAI,aAAa,EAAE,YAAY,IAAI,UAAU,EAAE,EAAE,GAAG;EAExG;AACD,SAAO,eAAe,CAAC,KAAK,GAAI;AAChC,UAAQ,IAAI,KAAK,WAAW;AAC5B,UAAQ,IAAI,KAAK,WAAW;AAC5B,MAAI,WAAW,WAAW,EAAE;AAC1B,mBAAgB,IAAI,KAAK,WAAW;AACpC,UAAO,wBAAwB;EAChC;AACD,MAAI,WAAW,WAAW,EAAE;AAC1B,mBAAgB,IAAI,KAAK,WAAW;AACpC,GAAyC,OAAO,0BAAwB;EACzE;CACF;CACD,YAAY,SAAU,aAAa,QAAQ,SAAS,iBAAiB;EACnE,IAAI,kBAAkB,YAAY,uBAAuB,cAAc,iBAAiB,CAAC,OAAO;AAE9F,OAAK,gBACH,OAAM,IAAI,MAAM;AAGpB,SAAO,eAAe,CAAC,QAAS;AAChC,UAAQ,IAAI,UAAU,gBAAgB;AACtC,MAAI,WAAW,gBAAgB,EAAE;AAC/B,mBAAgB,IAAI,UAAU,gBAAgB;AAC9C,UAAO,wBAAwB;EAChC;CACF;CACD,OAAO,SAAU,aAAa,QAAQ,SAAS,iBAAiB;EAC9D,IAAI,aAAa,YAAY,uBAAuB,SAAS,iBAAiB,CAAC,OAAO;EACtF,IAAI,kBAAkB,WAAW,cAAc,aAAa;EAC5D,IAAI,iBAAiB,WAAW,cAAc,YAAY;EACf;AACzC,QAAK,eACH,OAAM,IAAI,MAAM;AAElB,QAAK,gBACH,OAAM,IAAI,MAAM;EAEnB;AACD,SAAO,eAAe,CAAC,UAAU,OAAQ;AACzC,UAAQ,IAAI,UAAU,gBAAgB;AACtC,UAAQ,IAAI,SAAS,eAAe;AACpC,MAAI,WAAW,gBAAgB,EAAE;AAC/B,mBAAgB,IAAI,UAAU,gBAAgB;AAC9C,UAAO,wBAAwB;EAChC;AACD,MAAI,WAAW,eAAe,EAAE;AAC9B,mBAAgB,IAAI,SAAS,eAAe;AAC5C,GAAyC,OAAO,0BAAwB;EACzE;CACF;CACD,KAAK,SAAU,aAAa,QAAQ,SAAS,iBAAiB;AAC5D,SAAO,eAAe,CAAC,OAAO,KAAM;CACrC;CACD,UAAU,SAAU,aAAa,QAAQ,SAAS,iBAAiB;EACjE,IAAI,UAAU,YAAY;EAC1B,IAAI,gBAAgB,QAAQ,aAAa,YAAY,YAAY,IAAI,gBAAgB,CAAC;EACtF,IAAI,eAAe,OAAO,eAAe,cAAc,WAAW,OAAO;AACzE,OAAK,cAAc,mBAAmB,SAAU,WAAW,OAAO;GAChE,IAAI,YAAY,QAAQ,aAAa,gBAAgB,UAAU;GAC/D,IAAI,UAAU,aAAa;AAC3B,WAAQ,IAAI,SAAS,UAAU;AAC/B,OAAI,WAAW,UAAU,EAAE;AACzB,oBAAgB,IAAI,SAAS,UAAU;AACvC,QAAI,OAAO,yBAAyB,KAClC,QAAO,wBAAwB;GAElC;EACF,EAAC;CACH;AACF;AACD,SAAS,WAAW,WAAW;AAC7B,QAAO,UAAU,IAAI,OAAO,KAAK;AAClC;;;;ACxHD,SAAS,mBAAmB,aAAa,cAAc;CACrD,IAAI,eAAe,YAAY,IAAI,mBAAmB;CACtD,IAAI,qBAAqB,yBAAiB,IAAI,aAAa;CAC3D,IAAI;AACJ,KAAI,gBAAgB,aAAa,aAC/B,mBAAkB,IAAW,aAAa,cAAc,SAAU,KAAK;EACrE,IAAI,UAAU,EACZ,MAAM,IACP;EACD,IAAI,YAAY,aAAa,QAAQ,IAAI,IAAI;AAC7C,MAAI,WAAW;GACb,IAAI,WAAW,UAAU,IAAI,OAAO;AACpC,WAAQ,OAAO,uBAAuB,SAAS;EAChD;AACD,SAAO;CACR,EAAC;AAEJ,MAAK,gBAEH,mBAAkB,uBAAuB,mBAAmB,oBAAoB,mBAAmB,mBAAmB,GAAG,mBAAmB,WAAW,OAAO,KAAK,CAAC,KAAK,GAAI;AAE/K,QAAO;AACR;AACD,SAAS,kBAAkB,aAAa,uBAAuB,cAAc;CAC3E,IAAI;CACJ,IAAI;AACJ,iBAAgB,KAAY,aAAa,SAAU,SAAS,UAAU;EACpE,IAAI,WAAW,QAAQ;EACvB,IAAI,oBAAoB,aAAa,gBAAgB,IAAI,SAAS;AAClE,MAAI,mBAAmB;AACrB,OAAI,yBAAyB,KAC3B,yBAAwB;AAE1B,WAAQ,cAAc,kBAAkB,gBAAgB;AACxD,OAAI,sBACF,SAAQ,wBAAwB;EAEnC;AACD,MAAI,QAAQ,UAAU,YAAY,KAChC,iBAAgB;CAEnB,EAAC;AACF,MAAK,iBAAiB,yBAAyB,KAC7C,aAAY,uBAAuB,UAAU,WAAW;AAE1D,QAAO;AACR;;;;;;;;AAKD,SAAS,iBAAiB,WAAW,aAAa,KAAK;AACrD,OAAM,OAAO,CAAE;CACf,IAAI,gBAAgB,YAAY,kBAAkB;CAClD,IAAI;CACJ,IAAI,mBAAmB;AACvB,KAAI,WAAW;AACb,qBAAmB;AACnB,WAAS,iCAAiC,UAAU;CACrD,OAAM;AACL,WAAS,cAAc,WAAW;AAElC,qBAAmB,OAAO,iBAAiB;CAC5C;CACD,IAAI,eAAe,wBAAwB,YAAY;CACvD,IAAI,kBAAkB,mBAAmB,aAAa,aAAa;CACnE,IAAI,qBAAqB,IAAI;CAC7B,IAAI,kBAAkB,WAAkB,mBAAmB,GAAG,qBAAqB,qBAAqB,MAAa,iCAAiC,iBAAiB,YAAY,GAAG;CACtL,IAAI,yBAAyB;EAC3B,iBAAiB;EACjB,eAAe,IAAI;EACnB,cAAc,YAAY,WAAW;EACpB;EACjB,0BAA0B;CAC3B;CACD,IAAI,SAAS,wBAAwB,QAAQ,uBAAuB;CACpE,IAAI,wBAAwB,kBAAkB,OAAO,YAAY,IAAI,uBAAuB,aAAa;CACzG,IAAI,SAAS,mBAAmB,cAAc,mBAAmB,OAAO,GAAG;CAC3E,IAAI,uBAAuB,gBAAgB,aAAa;EAC9C;EACD;CACR,EAAC;CACF,IAAI,OAAO,IAAIC,mBAAW,QAAQ;AAClC,MAAK,mBAAmB,qBAAqB;CAC7C,IAAI,iBAAiB,yBAAyB,QAAQ,0BAA0B,OAAO,GAAG,SAAU,SAAS,SAAS,WAAW,UAAU;AAEzI,SAAO,aAAa,wBAAwB,YAAY,KAAK,sBAAsB,SAAS,SAAS,WAAW,SAAS;CAC1H,IAAG;AACJ,MAAK,gBAAgB;AACrB,MAAK,SAEL,mBAAmB,SAAS,OAAO,MAAM,eAAe;AACxD,QAAO;AACR;AACD,SAAS,0BAA0B,QAAQ;AACzC,KAAI,OAAO,iBAAiB,wBAAwB;EAClD,IAAI,aAAa,iBAAiB,OAAO,QAAQ,CAAE,EAAC;AACpD,UAAQ,QAAe,iBAAiB,WAAW,CAAC;CACrD;AACF;AACD,SAAS,iBAAiB,KAAK;CAC7B,IAAI,IAAI;AACR,QAAO,IAAI,IAAI,UAAU,IAAI,MAAM,KACjC;AAEF,QAAO,IAAI;AACZ;AACD,+BAAe"}