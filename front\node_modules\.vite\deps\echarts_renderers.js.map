{"version": 3, "file": "echarts_renderers.js", "names": ["SVGPathRebuilder", "ZRImage", "el", "SVGPathRebuilder", "CompoundPath", "animators", "len", "animator", "i", "percent", "PathProxy", "SVGPathRebuilder", "brush", "Path", "ZRImage", "TSpan", "parentNode", "map", "SVGPainter", "install", "SVGPainter", "Layer", "BoundingRect", "i", "width", "height", "Eventful", "CanvasPainter", "Layer", "k", "layer", "env", "CanvasPainter"], "sources": ["../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/svg/SVGPathRebuilder.js", "../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/svg/mapStyleToAttrs.js", "../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/svg/core.js", "../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/svg/cssClassId.js", "../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/svg/cssAnimation.js", "../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/svg/cssEmphasis.js", "../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/svg/graphic.js", "../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/svg/domapi.js", "../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/svg/patch.js", "../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/svg/Painter.js", "../../.pnpm/echarts@5.6.0/node_modules/echarts/lib/renderer/installSVGRenderer.js", "../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/canvas/Layer.js", "../../.pnpm/zrender@5.6.1/node_modules/zrender/lib/canvas/Painter.js", "../../.pnpm/echarts@5.6.0/node_modules/echarts/lib/renderer/installCanvasRenderer.js"], "sourcesContent": ["import { isAroundZero } from './helper.js';\nvar mathSin = Math.sin;\nvar mathCos = Math.cos;\nvar PI = Math.PI;\nvar PI2 = Math.PI * 2;\nvar degree = 180 / PI;\nvar SVGPathRebuilder = (function () {\n    function SVGPathRebuilder() {\n    }\n    SVGPathRebuilder.prototype.reset = function (precision) {\n        this._start = true;\n        this._d = [];\n        this._str = '';\n        this._p = Math.pow(10, precision || 4);\n    };\n    SVGPathRebuilder.prototype.moveTo = function (x, y) {\n        this._add('M', x, y);\n    };\n    SVGPathRebuilder.prototype.lineTo = function (x, y) {\n        this._add('L', x, y);\n    };\n    SVGPathRebuilder.prototype.bezierCurveTo = function (x, y, x2, y2, x3, y3) {\n        this._add('C', x, y, x2, y2, x3, y3);\n    };\n    SVGPathRebuilder.prototype.quadraticCurveTo = function (x, y, x2, y2) {\n        this._add('Q', x, y, x2, y2);\n    };\n    SVGPathRebuilder.prototype.arc = function (cx, cy, r, startAngle, endAngle, anticlockwise) {\n        this.ellipse(cx, cy, r, r, 0, startAngle, endAngle, anticlockwise);\n    };\n    SVGPathRebuilder.prototype.ellipse = function (cx, cy, rx, ry, psi, startAngle, endAngle, anticlockwise) {\n        var dTheta = endAngle - startAngle;\n        var clockwise = !anticlockwise;\n        var dThetaPositive = Math.abs(dTheta);\n        var isCircle = isAroundZero(dThetaPositive - PI2)\n            || (clockwise ? dTheta >= PI2 : -dTheta >= PI2);\n        var unifiedTheta = dTheta > 0 ? dTheta % PI2 : (dTheta % PI2 + PI2);\n        var large = false;\n        if (isCircle) {\n            large = true;\n        }\n        else if (isAroundZero(dThetaPositive)) {\n            large = false;\n        }\n        else {\n            large = (unifiedTheta >= PI) === !!clockwise;\n        }\n        var x0 = cx + rx * mathCos(startAngle);\n        var y0 = cy + ry * mathSin(startAngle);\n        if (this._start) {\n            this._add('M', x0, y0);\n        }\n        var xRot = Math.round(psi * degree);\n        if (isCircle) {\n            var p = 1 / this._p;\n            var dTheta_1 = (clockwise ? 1 : -1) * (PI2 - p);\n            this._add('A', rx, ry, xRot, 1, +clockwise, cx + rx * mathCos(startAngle + dTheta_1), cy + ry * mathSin(startAngle + dTheta_1));\n            if (p > 1e-2) {\n                this._add('A', rx, ry, xRot, 0, +clockwise, x0, y0);\n            }\n        }\n        else {\n            var x = cx + rx * mathCos(endAngle);\n            var y = cy + ry * mathSin(endAngle);\n            this._add('A', rx, ry, xRot, +large, +clockwise, x, y);\n        }\n    };\n    SVGPathRebuilder.prototype.rect = function (x, y, w, h) {\n        this._add('M', x, y);\n        this._add('l', w, 0);\n        this._add('l', 0, h);\n        this._add('l', -w, 0);\n        this._add('Z');\n    };\n    SVGPathRebuilder.prototype.closePath = function () {\n        if (this._d.length > 0) {\n            this._add('Z');\n        }\n    };\n    SVGPathRebuilder.prototype._add = function (cmd, a, b, c, d, e, f, g, h) {\n        var vals = [];\n        var p = this._p;\n        for (var i = 1; i < arguments.length; i++) {\n            var val = arguments[i];\n            if (isNaN(val)) {\n                this._invalid = true;\n                return;\n            }\n            vals.push(Math.round(val * p) / p);\n        }\n        this._d.push(cmd + vals.join(' '));\n        this._start = cmd === 'Z';\n    };\n    SVGPathRebuilder.prototype.generateStr = function () {\n        this._str = this._invalid ? '' : this._d.join('');\n        this._d = [];\n    };\n    SVGPathRebuilder.prototype.getStr = function () {\n        return this._str;\n    };\n    return SVGPathRebuilder;\n}());\nexport default SVGPathRebuilder;\n", "import { DEFAULT_PATH_STYLE } from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport { getLineDash } from '../canvas/dashStyle.js';\nimport { map } from '../core/util.js';\nimport { normalizeColor } from './helper.js';\nvar NONE = 'none';\nvar mathRound = Math.round;\nfunction pathHasFill(style) {\n    var fill = style.fill;\n    return fill != null && fill !== NONE;\n}\nfunction pathHasStroke(style) {\n    var stroke = style.stroke;\n    return stroke != null && stroke !== NONE;\n}\nvar strokeProps = ['lineCap', 'miterLimit', 'lineJoin'];\nvar svgStrokeProps = map(strokeProps, function (prop) { return \"stroke-\" + prop.toLowerCase(); });\nexport default function mapStyleToAttrs(updateAttr, style, el, forceUpdate) {\n    var opacity = style.opacity == null ? 1 : style.opacity;\n    if (el instanceof ZRImage) {\n        updateAttr('opacity', opacity);\n        return;\n    }\n    if (pathHasFill(style)) {\n        var fill = normalizeColor(style.fill);\n        updateAttr('fill', fill.color);\n        var fillOpacity = style.fillOpacity != null\n            ? style.fillOpacity * fill.opacity * opacity\n            : fill.opacity * opacity;\n        if (forceUpdate || fillOpacity < 1) {\n            updateAttr('fill-opacity', fillOpacity);\n        }\n    }\n    else {\n        updateAttr('fill', NONE);\n    }\n    if (pathHasStroke(style)) {\n        var stroke = normalizeColor(style.stroke);\n        updateAttr('stroke', stroke.color);\n        var strokeScale = style.strokeNoScale\n            ? el.getLineScale()\n            : 1;\n        var strokeWidth = (strokeScale ? (style.lineWidth || 0) / strokeScale : 0);\n        var strokeOpacity = style.strokeOpacity != null\n            ? style.strokeOpacity * stroke.opacity * opacity\n            : stroke.opacity * opacity;\n        var strokeFirst = style.strokeFirst;\n        if (forceUpdate || strokeWidth !== 1) {\n            updateAttr('stroke-width', strokeWidth);\n        }\n        if (forceUpdate || strokeFirst) {\n            updateAttr('paint-order', strokeFirst ? 'stroke' : 'fill');\n        }\n        if (forceUpdate || strokeOpacity < 1) {\n            updateAttr('stroke-opacity', strokeOpacity);\n        }\n        if (style.lineDash) {\n            var _a = getLineDash(el), lineDash = _a[0], lineDashOffset = _a[1];\n            if (lineDash) {\n                lineDashOffset = mathRound(lineDashOffset || 0);\n                updateAttr('stroke-dasharray', lineDash.join(','));\n                if (lineDashOffset || forceUpdate) {\n                    updateAttr('stroke-dashoffset', lineDashOffset);\n                }\n            }\n        }\n        else if (forceUpdate) {\n            updateAttr('stroke-dasharray', NONE);\n        }\n        for (var i = 0; i < strokeProps.length; i++) {\n            var propName = strokeProps[i];\n            if (forceUpdate || style[propName] !== DEFAULT_PATH_STYLE[propName]) {\n                var val = style[propName] || DEFAULT_PATH_STYLE[propName];\n                val && updateAttr(svgStrokeProps[i], val);\n            }\n        }\n    }\n    else if (forceUpdate) {\n        updateAttr('stroke', NONE);\n    }\n}\n", "import { keys, map } from '../core/util.js';\nimport { encodeHTML } from '../core/dom.js';\nexport var SVGNS = 'http://www.w3.org/2000/svg';\nexport var XLINKNS = 'http://www.w3.org/1999/xlink';\nexport var XMLNS = 'http://www.w3.org/2000/xmlns/';\nexport var XML_NAMESPACE = 'http://www.w3.org/XML/1998/namespace';\nexport var META_DATA_PREFIX = 'ecmeta_';\nexport function createElement(name) {\n    return document.createElementNS(SVGNS, name);\n}\n;\nexport function createVNode(tag, key, attrs, children, text) {\n    return {\n        tag: tag,\n        attrs: attrs || {},\n        children: children,\n        text: text,\n        key: key\n    };\n}\nfunction createElementOpen(name, attrs) {\n    var attrsStr = [];\n    if (attrs) {\n        for (var key in attrs) {\n            var val = attrs[key];\n            var part = key;\n            if (val === false) {\n                continue;\n            }\n            else if (val !== true && val != null) {\n                part += \"=\\\"\" + val + \"\\\"\";\n            }\n            attrsStr.push(part);\n        }\n    }\n    return \"<\" + name + \" \" + attrsStr.join(' ') + \">\";\n}\nfunction createElementClose(name) {\n    return \"</\" + name + \">\";\n}\nexport function vNodeToString(el, opts) {\n    opts = opts || {};\n    var S = opts.newline ? '\\n' : '';\n    function convertElToString(el) {\n        var children = el.children, tag = el.tag, attrs = el.attrs, text = el.text;\n        return createElementOpen(tag, attrs)\n            + (tag !== 'style' ? encodeHTML(text) : text || '')\n            + (children ? \"\" + S + map(children, function (child) { return convertElToString(child); }).join(S) + S : '')\n            + createElementClose(tag);\n    }\n    return convertElToString(el);\n}\nexport function getCssString(selectorNodes, animationNodes, opts) {\n    opts = opts || {};\n    var S = opts.newline ? '\\n' : '';\n    var bracketBegin = \" {\" + S;\n    var bracketEnd = S + \"}\";\n    var selectors = map(keys(selectorNodes), function (className) {\n        return className + bracketBegin + map(keys(selectorNodes[className]), function (attrName) {\n            return attrName + \":\" + selectorNodes[className][attrName] + \";\";\n        }).join(S) + bracketEnd;\n    }).join(S);\n    var animations = map(keys(animationNodes), function (animationName) {\n        return \"@keyframes \" + animationName + bracketBegin + map(keys(animationNodes[animationName]), function (percent) {\n            return percent + bracketBegin + map(keys(animationNodes[animationName][percent]), function (attrName) {\n                var val = animationNodes[animationName][percent][attrName];\n                if (attrName === 'd') {\n                    val = \"path(\\\"\" + val + \"\\\")\";\n                }\n                return attrName + \":\" + val + \";\";\n            }).join(S) + bracketEnd;\n        }).join(S) + bracketEnd;\n    }).join(S);\n    if (!selectors && !animations) {\n        return '';\n    }\n    return ['<![CDATA[', selectors, animations, ']]>'].join(S);\n}\nexport function createBrushScope(zrId) {\n    return {\n        zrId: zrId,\n        shadowCache: {},\n        patternCache: {},\n        gradientCache: {},\n        clipPathCache: {},\n        defs: {},\n        cssNodes: {},\n        cssAnims: {},\n        cssStyleCache: {},\n        cssAnimIdx: 0,\n        shadowIdx: 0,\n        gradientIdx: 0,\n        patternIdx: 0,\n        clipPathIdx: 0\n    };\n}\nexport function createSVGVNode(width, height, children, useViewBox) {\n    return createVNode('svg', 'root', {\n        'width': width,\n        'height': height,\n        'xmlns': SVGNS,\n        'xmlns:xlink': XLINKNS,\n        'version': '1.1',\n        'baseProfile': 'full',\n        'viewBox': useViewBox ? \"0 0 \" + width + \" \" + height : false\n    }, children);\n}\n", "var cssClassIdx = 0;\nexport function getClassId() {\n    return cssClassIdx++;\n}\n", "import { copyTransform } from '../core/Transformable.js';\nimport { createBrushScope } from './core.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport PathProxy from '../core/PathProxy.js';\nimport { getPathPrecision, getSRTTransformString } from './helper.js';\nimport { each, extend, filter, isNumber, isString, keys } from '../core/util.js';\nimport CompoundPath from '../graphic/CompoundPath.js';\nimport { createCubicEasingFunc } from '../animation/cubicEasing.js';\nimport { getClassId } from './cssClassId.js';\nexport var EASING_MAP = {\n    cubicIn: '0.32,0,0.67,0',\n    cubicOut: '0.33,1,0.68,1',\n    cubicInOut: '0.65,0,0.35,1',\n    quadraticIn: '0.11,0,0.5,0',\n    quadraticOut: '0.5,1,0.89,1',\n    quadraticInOut: '0.45,0,0.55,1',\n    quarticIn: '0.5,0,0.75,0',\n    quarticOut: '0.25,1,0.5,1',\n    quarticInOut: '0.76,0,0.24,1',\n    quinticIn: '0.64,0,0.78,0',\n    quinticOut: '0.22,1,0.36,1',\n    quinticInOut: '0.83,0,0.17,1',\n    sinusoidalIn: '0.12,0,0.39,0',\n    sinusoidalOut: '0.61,1,0.88,1',\n    sinusoidalInOut: '0.37,0,0.63,1',\n    exponentialIn: '0.7,0,0.84,0',\n    exponentialOut: '0.16,1,0.3,1',\n    exponentialInOut: '0.87,0,0.13,1',\n    circularIn: '0.55,0,1,0.45',\n    circularOut: '0,0.55,0.45,1',\n    circularInOut: '0.85,0,0.15,1'\n};\nvar transformOriginKey = 'transform-origin';\nfunction buildPathString(el, kfShape, path) {\n    var shape = extend({}, el.shape);\n    extend(shape, kfShape);\n    el.buildPath(path, shape);\n    var svgPathBuilder = new SVGPathRebuilder();\n    svgPathBuilder.reset(getPathPrecision(el));\n    path.rebuildPath(svgPathBuilder, 1);\n    svgPathBuilder.generateStr();\n    return svgPathBuilder.getStr();\n}\nfunction setTransformOrigin(target, transform) {\n    var originX = transform.originX, originY = transform.originY;\n    if (originX || originY) {\n        target[transformOriginKey] = originX + \"px \" + originY + \"px\";\n    }\n}\nexport var ANIMATE_STYLE_MAP = {\n    fill: 'fill',\n    opacity: 'opacity',\n    lineWidth: 'stroke-width',\n    lineDashOffset: 'stroke-dashoffset'\n};\nfunction addAnimation(cssAnim, scope) {\n    var animationName = scope.zrId + '-ani-' + scope.cssAnimIdx++;\n    scope.cssAnims[animationName] = cssAnim;\n    return animationName;\n}\nfunction createCompoundPathCSSAnimation(el, attrs, scope) {\n    var paths = el.shape.paths;\n    var composedAnim = {};\n    var cssAnimationCfg;\n    var cssAnimationName;\n    each(paths, function (path) {\n        var subScope = createBrushScope(scope.zrId);\n        subScope.animation = true;\n        createCSSAnimation(path, {}, subScope, true);\n        var cssAnims = subScope.cssAnims;\n        var cssNodes = subScope.cssNodes;\n        var animNames = keys(cssAnims);\n        var len = animNames.length;\n        if (!len) {\n            return;\n        }\n        cssAnimationName = animNames[len - 1];\n        var lastAnim = cssAnims[cssAnimationName];\n        for (var percent in lastAnim) {\n            var kf = lastAnim[percent];\n            composedAnim[percent] = composedAnim[percent] || { d: '' };\n            composedAnim[percent].d += kf.d || '';\n        }\n        for (var className in cssNodes) {\n            var val = cssNodes[className].animation;\n            if (val.indexOf(cssAnimationName) >= 0) {\n                cssAnimationCfg = val;\n            }\n        }\n    });\n    if (!cssAnimationCfg) {\n        return;\n    }\n    attrs.d = false;\n    var animationName = addAnimation(composedAnim, scope);\n    return cssAnimationCfg.replace(cssAnimationName, animationName);\n}\nfunction getEasingFunc(easing) {\n    return isString(easing)\n        ? EASING_MAP[easing]\n            ? \"cubic-bezier(\" + EASING_MAP[easing] + \")\"\n            : createCubicEasingFunc(easing) ? easing : ''\n        : '';\n}\nexport function createCSSAnimation(el, attrs, scope, onlyShape) {\n    var animators = el.animators;\n    var len = animators.length;\n    var cssAnimations = [];\n    if (el instanceof CompoundPath) {\n        var animationCfg = createCompoundPathCSSAnimation(el, attrs, scope);\n        if (animationCfg) {\n            cssAnimations.push(animationCfg);\n        }\n        else if (!len) {\n            return;\n        }\n    }\n    else if (!len) {\n        return;\n    }\n    var groupAnimators = {};\n    for (var i = 0; i < len; i++) {\n        var animator = animators[i];\n        var cfgArr = [animator.getMaxTime() / 1000 + 's'];\n        var easing = getEasingFunc(animator.getClip().easing);\n        var delay = animator.getDelay();\n        if (easing) {\n            cfgArr.push(easing);\n        }\n        else {\n            cfgArr.push('linear');\n        }\n        if (delay) {\n            cfgArr.push(delay / 1000 + 's');\n        }\n        if (animator.getLoop()) {\n            cfgArr.push('infinite');\n        }\n        var cfg = cfgArr.join(' ');\n        groupAnimators[cfg] = groupAnimators[cfg] || [cfg, []];\n        groupAnimators[cfg][1].push(animator);\n    }\n    function createSingleCSSAnimation(groupAnimator) {\n        var animators = groupAnimator[1];\n        var len = animators.length;\n        var transformKfs = {};\n        var shapeKfs = {};\n        var finalKfs = {};\n        var animationTimingFunctionAttrName = 'animation-timing-function';\n        function saveAnimatorTrackToCssKfs(animator, cssKfs, toCssAttrName) {\n            var tracks = animator.getTracks();\n            var maxTime = animator.getMaxTime();\n            for (var k = 0; k < tracks.length; k++) {\n                var track = tracks[k];\n                if (track.needsAnimate()) {\n                    var kfs = track.keyframes;\n                    var attrName = track.propName;\n                    toCssAttrName && (attrName = toCssAttrName(attrName));\n                    if (attrName) {\n                        for (var i = 0; i < kfs.length; i++) {\n                            var kf = kfs[i];\n                            var percent = Math.round(kf.time / maxTime * 100) + '%';\n                            var kfEasing = getEasingFunc(kf.easing);\n                            var rawValue = kf.rawValue;\n                            if (isString(rawValue) || isNumber(rawValue)) {\n                                cssKfs[percent] = cssKfs[percent] || {};\n                                cssKfs[percent][attrName] = kf.rawValue;\n                                if (kfEasing) {\n                                    cssKfs[percent][animationTimingFunctionAttrName] = kfEasing;\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        for (var i = 0; i < len; i++) {\n            var animator = animators[i];\n            var targetProp = animator.targetName;\n            if (!targetProp) {\n                !onlyShape && saveAnimatorTrackToCssKfs(animator, transformKfs);\n            }\n            else if (targetProp === 'shape') {\n                saveAnimatorTrackToCssKfs(animator, shapeKfs);\n            }\n        }\n        for (var percent in transformKfs) {\n            var transform = {};\n            copyTransform(transform, el);\n            extend(transform, transformKfs[percent]);\n            var str = getSRTTransformString(transform);\n            var timingFunction = transformKfs[percent][animationTimingFunctionAttrName];\n            finalKfs[percent] = str ? {\n                transform: str\n            } : {};\n            setTransformOrigin(finalKfs[percent], transform);\n            if (timingFunction) {\n                finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n            }\n        }\n        ;\n        var path;\n        var canAnimateShape = true;\n        for (var percent in shapeKfs) {\n            finalKfs[percent] = finalKfs[percent] || {};\n            var isFirst = !path;\n            var timingFunction = shapeKfs[percent][animationTimingFunctionAttrName];\n            if (isFirst) {\n                path = new PathProxy();\n            }\n            var len_1 = path.len();\n            path.reset();\n            finalKfs[percent].d = buildPathString(el, shapeKfs[percent], path);\n            var newLen = path.len();\n            if (!isFirst && len_1 !== newLen) {\n                canAnimateShape = false;\n                break;\n            }\n            if (timingFunction) {\n                finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n            }\n        }\n        ;\n        if (!canAnimateShape) {\n            for (var percent in finalKfs) {\n                delete finalKfs[percent].d;\n            }\n        }\n        if (!onlyShape) {\n            for (var i = 0; i < len; i++) {\n                var animator = animators[i];\n                var targetProp = animator.targetName;\n                if (targetProp === 'style') {\n                    saveAnimatorTrackToCssKfs(animator, finalKfs, function (propName) { return ANIMATE_STYLE_MAP[propName]; });\n                }\n            }\n        }\n        var percents = keys(finalKfs);\n        var allTransformOriginSame = true;\n        var transformOrigin;\n        for (var i = 1; i < percents.length; i++) {\n            var p0 = percents[i - 1];\n            var p1 = percents[i];\n            if (finalKfs[p0][transformOriginKey] !== finalKfs[p1][transformOriginKey]) {\n                allTransformOriginSame = false;\n                break;\n            }\n            transformOrigin = finalKfs[p0][transformOriginKey];\n        }\n        if (allTransformOriginSame && transformOrigin) {\n            for (var percent in finalKfs) {\n                if (finalKfs[percent][transformOriginKey]) {\n                    delete finalKfs[percent][transformOriginKey];\n                }\n            }\n            attrs[transformOriginKey] = transformOrigin;\n        }\n        if (filter(percents, function (percent) { return keys(finalKfs[percent]).length > 0; }).length) {\n            var animationName = addAnimation(finalKfs, scope);\n            return animationName + \" \" + groupAnimator[0] + \" both\";\n        }\n    }\n    for (var key in groupAnimators) {\n        var animationCfg = createSingleCSSAnimation(groupAnimators[key]);\n        if (animationCfg) {\n            cssAnimations.push(animationCfg);\n        }\n    }\n    if (cssAnimations.length) {\n        var className = scope.zrId + '-cls-' + getClassId();\n        scope.cssNodes['.' + className] = {\n            animation: cssAnimations.join(',')\n        };\n        attrs[\"class\"] = className;\n    }\n}\n", "import { liftColor } from '../tool/color.js';\nimport { getClassId } from './cssClassId.js';\nexport function createCSSEmphasis(el, attrs, scope) {\n    if (!el.ignore) {\n        if (el.isSilent()) {\n            var style = {\n                'pointer-events': 'none'\n            };\n            setClassAttribute(style, attrs, scope, true);\n        }\n        else {\n            var emphasisStyle = el.states.emphasis && el.states.emphasis.style\n                ? el.states.emphasis.style\n                : {};\n            var fill = emphasisStyle.fill;\n            if (!fill) {\n                var normalFill = el.style && el.style.fill;\n                var selectFill = el.states.select\n                    && el.states.select.style\n                    && el.states.select.style.fill;\n                var fromFill = el.currentStates.indexOf('select') >= 0\n                    ? (selectFill || normalFill)\n                    : normalFill;\n                if (fromFill) {\n                    fill = liftColor(fromFill);\n                }\n            }\n            var lineWidth = emphasisStyle.lineWidth;\n            if (lineWidth) {\n                var scaleX = (!emphasisStyle.strokeNoScale && el.transform)\n                    ? el.transform[0]\n                    : 1;\n                lineWidth = lineWidth / scaleX;\n            }\n            var style = {\n                cursor: 'pointer'\n            };\n            if (fill) {\n                style.fill = fill;\n            }\n            if (emphasisStyle.stroke) {\n                style.stroke = emphasisStyle.stroke;\n            }\n            if (lineWidth) {\n                style['stroke-width'] = lineWidth;\n            }\n            setClassAttribute(style, attrs, scope, true);\n        }\n    }\n}\nfunction setClassAttribute(style, attrs, scope, withHover) {\n    var styleKey = JSON.stringify(style);\n    var className = scope.cssStyleCache[styleKey];\n    if (!className) {\n        className = scope.zrId + '-cls-' + getClassId();\n        scope.cssStyleCache[styleKey] = className;\n        scope.cssNodes['.' + className + (withHover ? ':hover' : '')] = style;\n    }\n    attrs[\"class\"] = attrs[\"class\"] ? (attrs[\"class\"] + ' ' + className) : className;\n}\n", "import { adjustTextY, getIdURL, getMatrixStr, getPathPrecision, getShadow<PERSON>ey, getSRTTransformString, hasShadow, isAroundZero, isGradient, isImagePattern, isLinearGradient, isPattern, isRadialGradient, normalizeColor, round4, TEXT_ALIGN_TO_ANCHOR } from './helper.js';\nimport Path from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport { getLineHeight } from '../contain/text.js';\nimport TSpan from '../graphic/TSpan.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport mapStyleToAttrs from './mapStyleToAttrs.js';\nimport { createVNode, vNodeToString, META_DATA_PREFIX } from './core.js';\nimport { assert, clone, isFunction, isString, logError, map, retrieve2 } from '../core/util.js';\nimport { createOrUpdateImage } from '../graphic/helper/image.js';\nimport { createCSSAnimation } from './cssAnimation.js';\nimport { hasSeparateFont, parseFontSize } from '../graphic/Text.js';\nimport { DEFAULT_FONT, DEFAULT_FONT_FAMILY } from '../core/platform.js';\nimport { createCSSEmphasis } from './cssEmphasis.js';\nimport { getElementSSRData } from '../zrender.js';\nvar round = Math.round;\nfunction isImageLike(val) {\n    return val && isString(val.src);\n}\nfunction isCanvasLike(val) {\n    return val && isFunction(val.toDataURL);\n}\nfunction setStyleAttrs(attrs, style, el, scope) {\n    mapStyleToAttrs(function (key, val) {\n        var isFillStroke = key === 'fill' || key === 'stroke';\n        if (isFillStroke && isGradient(val)) {\n            setGradient(style, attrs, key, scope);\n        }\n        else if (isFillStroke && isPattern(val)) {\n            setPattern(el, attrs, key, scope);\n        }\n        else {\n            attrs[key] = val;\n        }\n        if (isFillStroke && scope.ssr && val === 'none') {\n            attrs['pointer-events'] = 'visible';\n        }\n    }, style, el, false);\n    setShadow(el, attrs, scope);\n}\nfunction setMetaData(attrs, el) {\n    var metaData = getElementSSRData(el);\n    if (metaData) {\n        metaData.each(function (val, key) {\n            val != null && (attrs[(META_DATA_PREFIX + key).toLowerCase()] = val + '');\n        });\n        if (el.isSilent()) {\n            attrs[META_DATA_PREFIX + 'silent'] = 'true';\n        }\n    }\n}\nfunction noRotateScale(m) {\n    return isAroundZero(m[0] - 1)\n        && isAroundZero(m[1])\n        && isAroundZero(m[2])\n        && isAroundZero(m[3] - 1);\n}\nfunction noTranslate(m) {\n    return isAroundZero(m[4]) && isAroundZero(m[5]);\n}\nfunction setTransform(attrs, m, compress) {\n    if (m && !(noTranslate(m) && noRotateScale(m))) {\n        var mul = compress ? 10 : 1e4;\n        attrs.transform = noRotateScale(m)\n            ? \"translate(\" + round(m[4] * mul) / mul + \" \" + round(m[5] * mul) / mul + \")\" : getMatrixStr(m);\n    }\n}\nfunction convertPolyShape(shape, attrs, mul) {\n    var points = shape.points;\n    var strArr = [];\n    for (var i = 0; i < points.length; i++) {\n        strArr.push(round(points[i][0] * mul) / mul);\n        strArr.push(round(points[i][1] * mul) / mul);\n    }\n    attrs.points = strArr.join(' ');\n}\nfunction validatePolyShape(shape) {\n    return !shape.smooth;\n}\nfunction createAttrsConvert(desc) {\n    var normalizedDesc = map(desc, function (item) {\n        return (typeof item === 'string' ? [item, item] : item);\n    });\n    return function (shape, attrs, mul) {\n        for (var i = 0; i < normalizedDesc.length; i++) {\n            var item = normalizedDesc[i];\n            var val = shape[item[0]];\n            if (val != null) {\n                attrs[item[1]] = round(val * mul) / mul;\n            }\n        }\n    };\n}\nvar builtinShapesDef = {\n    circle: [createAttrsConvert(['cx', 'cy', 'r'])],\n    polyline: [convertPolyShape, validatePolyShape],\n    polygon: [convertPolyShape, validatePolyShape]\n};\nfunction hasShapeAnimation(el) {\n    var animators = el.animators;\n    for (var i = 0; i < animators.length; i++) {\n        if (animators[i].targetName === 'shape') {\n            return true;\n        }\n    }\n    return false;\n}\nexport function brushSVGPath(el, scope) {\n    var style = el.style;\n    var shape = el.shape;\n    var builtinShpDef = builtinShapesDef[el.type];\n    var attrs = {};\n    var needsAnimate = scope.animation;\n    var svgElType = 'path';\n    var strokePercent = el.style.strokePercent;\n    var precision = (scope.compress && getPathPrecision(el)) || 4;\n    if (builtinShpDef\n        && !scope.willUpdate\n        && !(builtinShpDef[1] && !builtinShpDef[1](shape))\n        && !(needsAnimate && hasShapeAnimation(el))\n        && !(strokePercent < 1)) {\n        svgElType = el.type;\n        var mul = Math.pow(10, precision);\n        builtinShpDef[0](shape, attrs, mul);\n    }\n    else {\n        var needBuildPath = !el.path || el.shapeChanged();\n        if (!el.path) {\n            el.createPathProxy();\n        }\n        var path = el.path;\n        if (needBuildPath) {\n            path.beginPath();\n            el.buildPath(path, el.shape);\n            el.pathUpdated();\n        }\n        var pathVersion = path.getVersion();\n        var elExt = el;\n        var svgPathBuilder = elExt.__svgPathBuilder;\n        if (elExt.__svgPathVersion !== pathVersion\n            || !svgPathBuilder\n            || strokePercent !== elExt.__svgPathStrokePercent) {\n            if (!svgPathBuilder) {\n                svgPathBuilder = elExt.__svgPathBuilder = new SVGPathRebuilder();\n            }\n            svgPathBuilder.reset(precision);\n            path.rebuildPath(svgPathBuilder, strokePercent);\n            svgPathBuilder.generateStr();\n            elExt.__svgPathVersion = pathVersion;\n            elExt.__svgPathStrokePercent = strokePercent;\n        }\n        attrs.d = svgPathBuilder.getStr();\n    }\n    setTransform(attrs, el.transform);\n    setStyleAttrs(attrs, style, el, scope);\n    setMetaData(attrs, el);\n    scope.animation && createCSSAnimation(el, attrs, scope);\n    scope.emphasis && createCSSEmphasis(el, attrs, scope);\n    return createVNode(svgElType, el.id + '', attrs);\n}\nexport function brushSVGImage(el, scope) {\n    var style = el.style;\n    var image = style.image;\n    if (image && !isString(image)) {\n        if (isImageLike(image)) {\n            image = image.src;\n        }\n        else if (isCanvasLike(image)) {\n            image = image.toDataURL();\n        }\n    }\n    if (!image) {\n        return;\n    }\n    var x = style.x || 0;\n    var y = style.y || 0;\n    var dw = style.width;\n    var dh = style.height;\n    var attrs = {\n        href: image,\n        width: dw,\n        height: dh\n    };\n    if (x) {\n        attrs.x = x;\n    }\n    if (y) {\n        attrs.y = y;\n    }\n    setTransform(attrs, el.transform);\n    setStyleAttrs(attrs, style, el, scope);\n    setMetaData(attrs, el);\n    scope.animation && createCSSAnimation(el, attrs, scope);\n    return createVNode('image', el.id + '', attrs);\n}\n;\nexport function brushSVGTSpan(el, scope) {\n    var style = el.style;\n    var text = style.text;\n    text != null && (text += '');\n    if (!text || isNaN(style.x) || isNaN(style.y)) {\n        return;\n    }\n    var font = style.font || DEFAULT_FONT;\n    var x = style.x || 0;\n    var y = adjustTextY(style.y || 0, getLineHeight(font), style.textBaseline);\n    var textAlign = TEXT_ALIGN_TO_ANCHOR[style.textAlign]\n        || style.textAlign;\n    var attrs = {\n        'dominant-baseline': 'central',\n        'text-anchor': textAlign\n    };\n    if (hasSeparateFont(style)) {\n        var separatedFontStr = '';\n        var fontStyle = style.fontStyle;\n        var fontSize = parseFontSize(style.fontSize);\n        if (!parseFloat(fontSize)) {\n            return;\n        }\n        var fontFamily = style.fontFamily || DEFAULT_FONT_FAMILY;\n        var fontWeight = style.fontWeight;\n        separatedFontStr += \"font-size:\" + fontSize + \";font-family:\" + fontFamily + \";\";\n        if (fontStyle && fontStyle !== 'normal') {\n            separatedFontStr += \"font-style:\" + fontStyle + \";\";\n        }\n        if (fontWeight && fontWeight !== 'normal') {\n            separatedFontStr += \"font-weight:\" + fontWeight + \";\";\n        }\n        attrs.style = separatedFontStr;\n    }\n    else {\n        attrs.style = \"font: \" + font;\n    }\n    if (text.match(/\\s/)) {\n        attrs['xml:space'] = 'preserve';\n    }\n    if (x) {\n        attrs.x = x;\n    }\n    if (y) {\n        attrs.y = y;\n    }\n    setTransform(attrs, el.transform);\n    setStyleAttrs(attrs, style, el, scope);\n    setMetaData(attrs, el);\n    scope.animation && createCSSAnimation(el, attrs, scope);\n    return createVNode('text', el.id + '', attrs, undefined, text);\n}\nexport function brush(el, scope) {\n    if (el instanceof Path) {\n        return brushSVGPath(el, scope);\n    }\n    else if (el instanceof ZRImage) {\n        return brushSVGImage(el, scope);\n    }\n    else if (el instanceof TSpan) {\n        return brushSVGTSpan(el, scope);\n    }\n}\nfunction setShadow(el, attrs, scope) {\n    var style = el.style;\n    if (hasShadow(style)) {\n        var shadowKey = getShadowKey(el);\n        var shadowCache = scope.shadowCache;\n        var shadowId = shadowCache[shadowKey];\n        if (!shadowId) {\n            var globalScale = el.getGlobalScale();\n            var scaleX = globalScale[0];\n            var scaleY = globalScale[1];\n            if (!scaleX || !scaleY) {\n                return;\n            }\n            var offsetX = style.shadowOffsetX || 0;\n            var offsetY = style.shadowOffsetY || 0;\n            var blur_1 = style.shadowBlur;\n            var _a = normalizeColor(style.shadowColor), opacity = _a.opacity, color = _a.color;\n            var stdDx = blur_1 / 2 / scaleX;\n            var stdDy = blur_1 / 2 / scaleY;\n            var stdDeviation = stdDx + ' ' + stdDy;\n            shadowId = scope.zrId + '-s' + scope.shadowIdx++;\n            scope.defs[shadowId] = createVNode('filter', shadowId, {\n                'id': shadowId,\n                'x': '-100%',\n                'y': '-100%',\n                'width': '300%',\n                'height': '300%'\n            }, [\n                createVNode('feDropShadow', '', {\n                    'dx': offsetX / scaleX,\n                    'dy': offsetY / scaleY,\n                    'stdDeviation': stdDeviation,\n                    'flood-color': color,\n                    'flood-opacity': opacity\n                })\n            ]);\n            shadowCache[shadowKey] = shadowId;\n        }\n        attrs.filter = getIdURL(shadowId);\n    }\n}\nexport function setGradient(style, attrs, target, scope) {\n    var val = style[target];\n    var gradientTag;\n    var gradientAttrs = {\n        'gradientUnits': val.global\n            ? 'userSpaceOnUse'\n            : 'objectBoundingBox'\n    };\n    if (isLinearGradient(val)) {\n        gradientTag = 'linearGradient';\n        gradientAttrs.x1 = val.x;\n        gradientAttrs.y1 = val.y;\n        gradientAttrs.x2 = val.x2;\n        gradientAttrs.y2 = val.y2;\n    }\n    else if (isRadialGradient(val)) {\n        gradientTag = 'radialGradient';\n        gradientAttrs.cx = retrieve2(val.x, 0.5);\n        gradientAttrs.cy = retrieve2(val.y, 0.5);\n        gradientAttrs.r = retrieve2(val.r, 0.5);\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            logError('Illegal gradient type.');\n        }\n        return;\n    }\n    var colors = val.colorStops;\n    var colorStops = [];\n    for (var i = 0, len = colors.length; i < len; ++i) {\n        var offset = round4(colors[i].offset) * 100 + '%';\n        var stopColor = colors[i].color;\n        var _a = normalizeColor(stopColor), color = _a.color, opacity = _a.opacity;\n        var stopsAttrs = {\n            'offset': offset\n        };\n        stopsAttrs['stop-color'] = color;\n        if (opacity < 1) {\n            stopsAttrs['stop-opacity'] = opacity;\n        }\n        colorStops.push(createVNode('stop', i + '', stopsAttrs));\n    }\n    var gradientVNode = createVNode(gradientTag, '', gradientAttrs, colorStops);\n    var gradientKey = vNodeToString(gradientVNode);\n    var gradientCache = scope.gradientCache;\n    var gradientId = gradientCache[gradientKey];\n    if (!gradientId) {\n        gradientId = scope.zrId + '-g' + scope.gradientIdx++;\n        gradientCache[gradientKey] = gradientId;\n        gradientAttrs.id = gradientId;\n        scope.defs[gradientId] = createVNode(gradientTag, gradientId, gradientAttrs, colorStops);\n    }\n    attrs[target] = getIdURL(gradientId);\n}\nexport function setPattern(el, attrs, target, scope) {\n    var val = el.style[target];\n    var boundingRect = el.getBoundingRect();\n    var patternAttrs = {};\n    var repeat = val.repeat;\n    var noRepeat = repeat === 'no-repeat';\n    var repeatX = repeat === 'repeat-x';\n    var repeatY = repeat === 'repeat-y';\n    var child;\n    if (isImagePattern(val)) {\n        var imageWidth_1 = val.imageWidth;\n        var imageHeight_1 = val.imageHeight;\n        var imageSrc = void 0;\n        var patternImage = val.image;\n        if (isString(patternImage)) {\n            imageSrc = patternImage;\n        }\n        else if (isImageLike(patternImage)) {\n            imageSrc = patternImage.src;\n        }\n        else if (isCanvasLike(patternImage)) {\n            imageSrc = patternImage.toDataURL();\n        }\n        if (typeof Image === 'undefined') {\n            var errMsg = 'Image width/height must been given explictly in svg-ssr renderer.';\n            assert(imageWidth_1, errMsg);\n            assert(imageHeight_1, errMsg);\n        }\n        else if (imageWidth_1 == null || imageHeight_1 == null) {\n            var setSizeToVNode_1 = function (vNode, img) {\n                if (vNode) {\n                    var svgEl = vNode.elm;\n                    var width = imageWidth_1 || img.width;\n                    var height = imageHeight_1 || img.height;\n                    if (vNode.tag === 'pattern') {\n                        if (repeatX) {\n                            height = 1;\n                            width /= boundingRect.width;\n                        }\n                        else if (repeatY) {\n                            width = 1;\n                            height /= boundingRect.height;\n                        }\n                    }\n                    vNode.attrs.width = width;\n                    vNode.attrs.height = height;\n                    if (svgEl) {\n                        svgEl.setAttribute('width', width);\n                        svgEl.setAttribute('height', height);\n                    }\n                }\n            };\n            var createdImage = createOrUpdateImage(imageSrc, null, el, function (img) {\n                noRepeat || setSizeToVNode_1(patternVNode, img);\n                setSizeToVNode_1(child, img);\n            });\n            if (createdImage && createdImage.width && createdImage.height) {\n                imageWidth_1 = imageWidth_1 || createdImage.width;\n                imageHeight_1 = imageHeight_1 || createdImage.height;\n            }\n        }\n        child = createVNode('image', 'img', {\n            href: imageSrc,\n            width: imageWidth_1,\n            height: imageHeight_1\n        });\n        patternAttrs.width = imageWidth_1;\n        patternAttrs.height = imageHeight_1;\n    }\n    else if (val.svgElement) {\n        child = clone(val.svgElement);\n        patternAttrs.width = val.svgWidth;\n        patternAttrs.height = val.svgHeight;\n    }\n    if (!child) {\n        return;\n    }\n    var patternWidth;\n    var patternHeight;\n    if (noRepeat) {\n        patternWidth = patternHeight = 1;\n    }\n    else if (repeatX) {\n        patternHeight = 1;\n        patternWidth = patternAttrs.width / boundingRect.width;\n    }\n    else if (repeatY) {\n        patternWidth = 1;\n        patternHeight = patternAttrs.height / boundingRect.height;\n    }\n    else {\n        patternAttrs.patternUnits = 'userSpaceOnUse';\n    }\n    if (patternWidth != null && !isNaN(patternWidth)) {\n        patternAttrs.width = patternWidth;\n    }\n    if (patternHeight != null && !isNaN(patternHeight)) {\n        patternAttrs.height = patternHeight;\n    }\n    var patternTransform = getSRTTransformString(val);\n    patternTransform && (patternAttrs.patternTransform = patternTransform);\n    var patternVNode = createVNode('pattern', '', patternAttrs, [child]);\n    var patternKey = vNodeToString(patternVNode);\n    var patternCache = scope.patternCache;\n    var patternId = patternCache[patternKey];\n    if (!patternId) {\n        patternId = scope.zrId + '-p' + scope.patternIdx++;\n        patternCache[patternKey] = patternId;\n        patternAttrs.id = patternId;\n        patternVNode = scope.defs[patternId] = createVNode('pattern', patternId, patternAttrs, [child]);\n    }\n    attrs[target] = getIdURL(patternId);\n}\nexport function setClipPath(clipPath, attrs, scope) {\n    var clipPathCache = scope.clipPathCache, defs = scope.defs;\n    var clipPathId = clipPathCache[clipPath.id];\n    if (!clipPathId) {\n        clipPathId = scope.zrId + '-c' + scope.clipPathIdx++;\n        var clipPathAttrs = {\n            id: clipPathId\n        };\n        clipPathCache[clipPath.id] = clipPathId;\n        defs[clipPathId] = createVNode('clipPath', clipPathId, clipPathAttrs, [brushSVGPath(clipPath, scope)]);\n    }\n    attrs['clip-path'] = getIdURL(clipPathId);\n}\n", "export function createTextNode(text) {\n    return document.createTextNode(text);\n}\nexport function createComment(text) {\n    return document.createComment(text);\n}\nexport function insertBefore(parentNode, newNode, referenceNode) {\n    parentNode.insertBefore(newNode, referenceNode);\n}\nexport function removeChild(node, child) {\n    node.removeChild(child);\n}\nexport function appendChild(node, child) {\n    node.appendChild(child);\n}\nexport function parentNode(node) {\n    return node.parentNode;\n}\nexport function nextSibling(node) {\n    return node.nextSibling;\n}\nexport function tagName(elm) {\n    return elm.tagName;\n}\nexport function setTextContent(node, text) {\n    node.textContent = text;\n}\nexport function getTextContent(node) {\n    return node.textContent;\n}\nexport function isElement(node) {\n    return node.nodeType === 1;\n}\nexport function isText(node) {\n    return node.nodeType === 3;\n}\nexport function isComment(node) {\n    return node.nodeType === 8;\n}\n", "import { isArray, isObject } from '../core/util.js';\nimport { createElement, createVNode, XMLNS, XML_NAMESPACE, XLINKNS } from './core.js';\nimport * as api from './domapi.js';\nvar colonChar = 58;\nvar xChar = 120;\nvar emptyNode = createVNode('', '');\nfunction isUndef(s) {\n    return s === undefined;\n}\nfunction isDef(s) {\n    return s !== undefined;\n}\nfunction createKeyToOldIdx(children, beginIdx, endIdx) {\n    var map = {};\n    for (var i = beginIdx; i <= endIdx; ++i) {\n        var key = children[i].key;\n        if (key !== undefined) {\n            if (process.env.NODE_ENV !== 'production') {\n                if (map[key] != null) {\n                    console.error(\"Duplicate key \" + key);\n                }\n            }\n            map[key] = i;\n        }\n    }\n    return map;\n}\nfunction sameVnode(vnode1, vnode2) {\n    var isSameKey = vnode1.key === vnode2.key;\n    var isSameTag = vnode1.tag === vnode2.tag;\n    return isSameTag && isSameKey;\n}\nfunction createElm(vnode) {\n    var i;\n    var children = vnode.children;\n    var tag = vnode.tag;\n    if (isDef(tag)) {\n        var elm = (vnode.elm = createElement(tag));\n        updateAttrs(emptyNode, vnode);\n        if (isArray(children)) {\n            for (i = 0; i < children.length; ++i) {\n                var ch = children[i];\n                if (ch != null) {\n                    api.appendChild(elm, createElm(ch));\n                }\n            }\n        }\n        else if (isDef(vnode.text) && !isObject(vnode.text)) {\n            api.appendChild(elm, api.createTextNode(vnode.text));\n        }\n    }\n    else {\n        vnode.elm = api.createTextNode(vnode.text);\n    }\n    return vnode.elm;\n}\nfunction addVnodes(parentElm, before, vnodes, startIdx, endIdx) {\n    for (; startIdx <= endIdx; ++startIdx) {\n        var ch = vnodes[startIdx];\n        if (ch != null) {\n            api.insertBefore(parentElm, createElm(ch), before);\n        }\n    }\n}\nfunction removeVnodes(parentElm, vnodes, startIdx, endIdx) {\n    for (; startIdx <= endIdx; ++startIdx) {\n        var ch = vnodes[startIdx];\n        if (ch != null) {\n            if (isDef(ch.tag)) {\n                var parent_1 = api.parentNode(ch.elm);\n                api.removeChild(parent_1, ch.elm);\n            }\n            else {\n                api.removeChild(parentElm, ch.elm);\n            }\n        }\n    }\n}\nexport function updateAttrs(oldVnode, vnode) {\n    var key;\n    var elm = vnode.elm;\n    var oldAttrs = oldVnode && oldVnode.attrs || {};\n    var attrs = vnode.attrs || {};\n    if (oldAttrs === attrs) {\n        return;\n    }\n    for (key in attrs) {\n        var cur = attrs[key];\n        var old = oldAttrs[key];\n        if (old !== cur) {\n            if (cur === true) {\n                elm.setAttribute(key, '');\n            }\n            else if (cur === false) {\n                elm.removeAttribute(key);\n            }\n            else {\n                if (key === 'style') {\n                    elm.style.cssText = cur;\n                }\n                else if (key.charCodeAt(0) !== xChar) {\n                    elm.setAttribute(key, cur);\n                }\n                else if (key === 'xmlns:xlink' || key === 'xmlns') {\n                    elm.setAttributeNS(XMLNS, key, cur);\n                }\n                else if (key.charCodeAt(3) === colonChar) {\n                    elm.setAttributeNS(XML_NAMESPACE, key, cur);\n                }\n                else if (key.charCodeAt(5) === colonChar) {\n                    elm.setAttributeNS(XLINKNS, key, cur);\n                }\n                else {\n                    elm.setAttribute(key, cur);\n                }\n            }\n        }\n    }\n    for (key in oldAttrs) {\n        if (!(key in attrs)) {\n            elm.removeAttribute(key);\n        }\n    }\n}\nfunction updateChildren(parentElm, oldCh, newCh) {\n    var oldStartIdx = 0;\n    var newStartIdx = 0;\n    var oldEndIdx = oldCh.length - 1;\n    var oldStartVnode = oldCh[0];\n    var oldEndVnode = oldCh[oldEndIdx];\n    var newEndIdx = newCh.length - 1;\n    var newStartVnode = newCh[0];\n    var newEndVnode = newCh[newEndIdx];\n    var oldKeyToIdx;\n    var idxInOld;\n    var elmToMove;\n    var before;\n    while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n        if (oldStartVnode == null) {\n            oldStartVnode = oldCh[++oldStartIdx];\n        }\n        else if (oldEndVnode == null) {\n            oldEndVnode = oldCh[--oldEndIdx];\n        }\n        else if (newStartVnode == null) {\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else if (newEndVnode == null) {\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (sameVnode(oldStartVnode, newStartVnode)) {\n            patchVnode(oldStartVnode, newStartVnode);\n            oldStartVnode = oldCh[++oldStartIdx];\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else if (sameVnode(oldEndVnode, newEndVnode)) {\n            patchVnode(oldEndVnode, newEndVnode);\n            oldEndVnode = oldCh[--oldEndIdx];\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (sameVnode(oldStartVnode, newEndVnode)) {\n            patchVnode(oldStartVnode, newEndVnode);\n            api.insertBefore(parentElm, oldStartVnode.elm, api.nextSibling(oldEndVnode.elm));\n            oldStartVnode = oldCh[++oldStartIdx];\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (sameVnode(oldEndVnode, newStartVnode)) {\n            patchVnode(oldEndVnode, newStartVnode);\n            api.insertBefore(parentElm, oldEndVnode.elm, oldStartVnode.elm);\n            oldEndVnode = oldCh[--oldEndIdx];\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else {\n            if (isUndef(oldKeyToIdx)) {\n                oldKeyToIdx = createKeyToOldIdx(oldCh, oldStartIdx, oldEndIdx);\n            }\n            idxInOld = oldKeyToIdx[newStartVnode.key];\n            if (isUndef(idxInOld)) {\n                api.insertBefore(parentElm, createElm(newStartVnode), oldStartVnode.elm);\n            }\n            else {\n                elmToMove = oldCh[idxInOld];\n                if (elmToMove.tag !== newStartVnode.tag) {\n                    api.insertBefore(parentElm, createElm(newStartVnode), oldStartVnode.elm);\n                }\n                else {\n                    patchVnode(elmToMove, newStartVnode);\n                    oldCh[idxInOld] = undefined;\n                    api.insertBefore(parentElm, elmToMove.elm, oldStartVnode.elm);\n                }\n            }\n            newStartVnode = newCh[++newStartIdx];\n        }\n    }\n    if (oldStartIdx <= oldEndIdx || newStartIdx <= newEndIdx) {\n        if (oldStartIdx > oldEndIdx) {\n            before = newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].elm;\n            addVnodes(parentElm, before, newCh, newStartIdx, newEndIdx);\n        }\n        else {\n            removeVnodes(parentElm, oldCh, oldStartIdx, oldEndIdx);\n        }\n    }\n}\nfunction patchVnode(oldVnode, vnode) {\n    var elm = (vnode.elm = oldVnode.elm);\n    var oldCh = oldVnode.children;\n    var ch = vnode.children;\n    if (oldVnode === vnode) {\n        return;\n    }\n    updateAttrs(oldVnode, vnode);\n    if (isUndef(vnode.text)) {\n        if (isDef(oldCh) && isDef(ch)) {\n            if (oldCh !== ch) {\n                updateChildren(elm, oldCh, ch);\n            }\n        }\n        else if (isDef(ch)) {\n            if (isDef(oldVnode.text)) {\n                api.setTextContent(elm, '');\n            }\n            addVnodes(elm, null, ch, 0, ch.length - 1);\n        }\n        else if (isDef(oldCh)) {\n            removeVnodes(elm, oldCh, 0, oldCh.length - 1);\n        }\n        else if (isDef(oldVnode.text)) {\n            api.setTextContent(elm, '');\n        }\n    }\n    else if (oldVnode.text !== vnode.text) {\n        if (isDef(oldCh)) {\n            removeVnodes(elm, oldCh, 0, oldCh.length - 1);\n        }\n        api.setTextContent(elm, vnode.text);\n    }\n}\nexport default function patch(oldVnode, vnode) {\n    if (sameVnode(oldVnode, vnode)) {\n        patchVnode(oldVnode, vnode);\n    }\n    else {\n        var elm = oldVnode.elm;\n        var parent_2 = api.parentNode(elm);\n        createElm(vnode);\n        if (parent_2 !== null) {\n            api.insertBefore(parent_2, vnode.elm, api.nextSibling(elm));\n            removeVnodes(parent_2, [oldVnode], 0, 0);\n        }\n    }\n    return vnode;\n}\n", "import { brush, setClipPath, setGradient, setPattern } from './graphic.js';\nimport { createElement, createVNode, vNodeToString, getCssString, createBrushScope, createSVGVNode } from './core.js';\nimport { normalizeColor, encodeBase64, isGradient, isPattern } from './helper.js';\nimport { extend, keys, logError, map, noop, retrieve2 } from '../core/util.js';\nimport patch, { updateAttrs } from './patch.js';\nimport { getSize } from '../canvas/helper.js';\nvar svgId = 0;\nvar SVGPainter = (function () {\n    function SVGPainter(root, storage, opts) {\n        this.type = 'svg';\n        this.refreshHover = createMethodNotSupport('refreshHover');\n        this.configLayer = createMethodNotSupport('configLayer');\n        this.storage = storage;\n        this._opts = opts = extend({}, opts);\n        this.root = root;\n        this._id = 'zr' + svgId++;\n        this._oldVNode = createSVGVNode(opts.width, opts.height);\n        if (root && !opts.ssr) {\n            var viewport = this._viewport = document.createElement('div');\n            viewport.style.cssText = 'position:relative;overflow:hidden';\n            var svgDom = this._svgDom = this._oldVNode.elm = createElement('svg');\n            updateAttrs(null, this._oldVNode);\n            viewport.appendChild(svgDom);\n            root.appendChild(viewport);\n        }\n        this.resize(opts.width, opts.height);\n    }\n    SVGPainter.prototype.getType = function () {\n        return this.type;\n    };\n    SVGPainter.prototype.getViewportRoot = function () {\n        return this._viewport;\n    };\n    SVGPainter.prototype.getViewportRootOffset = function () {\n        var viewportRoot = this.getViewportRoot();\n        if (viewportRoot) {\n            return {\n                offsetLeft: viewportRoot.offsetLeft || 0,\n                offsetTop: viewportRoot.offsetTop || 0\n            };\n        }\n    };\n    SVGPainter.prototype.getSvgDom = function () {\n        return this._svgDom;\n    };\n    SVGPainter.prototype.refresh = function () {\n        if (this.root) {\n            var vnode = this.renderToVNode({\n                willUpdate: true\n            });\n            vnode.attrs.style = 'position:absolute;left:0;top:0;user-select:none';\n            patch(this._oldVNode, vnode);\n            this._oldVNode = vnode;\n        }\n    };\n    SVGPainter.prototype.renderOneToVNode = function (el) {\n        return brush(el, createBrushScope(this._id));\n    };\n    SVGPainter.prototype.renderToVNode = function (opts) {\n        opts = opts || {};\n        var list = this.storage.getDisplayList(true);\n        var width = this._width;\n        var height = this._height;\n        var scope = createBrushScope(this._id);\n        scope.animation = opts.animation;\n        scope.willUpdate = opts.willUpdate;\n        scope.compress = opts.compress;\n        scope.emphasis = opts.emphasis;\n        scope.ssr = this._opts.ssr;\n        var children = [];\n        var bgVNode = this._bgVNode = createBackgroundVNode(width, height, this._backgroundColor, scope);\n        bgVNode && children.push(bgVNode);\n        var mainVNode = !opts.compress\n            ? (this._mainVNode = createVNode('g', 'main', {}, [])) : null;\n        this._paintList(list, scope, mainVNode ? mainVNode.children : children);\n        mainVNode && children.push(mainVNode);\n        var defs = map(keys(scope.defs), function (id) { return scope.defs[id]; });\n        if (defs.length) {\n            children.push(createVNode('defs', 'defs', {}, defs));\n        }\n        if (opts.animation) {\n            var animationCssStr = getCssString(scope.cssNodes, scope.cssAnims, { newline: true });\n            if (animationCssStr) {\n                var styleNode = createVNode('style', 'stl', {}, [], animationCssStr);\n                children.push(styleNode);\n            }\n        }\n        return createSVGVNode(width, height, children, opts.useViewBox);\n    };\n    SVGPainter.prototype.renderToString = function (opts) {\n        opts = opts || {};\n        return vNodeToString(this.renderToVNode({\n            animation: retrieve2(opts.cssAnimation, true),\n            emphasis: retrieve2(opts.cssEmphasis, true),\n            willUpdate: false,\n            compress: true,\n            useViewBox: retrieve2(opts.useViewBox, true)\n        }), { newline: true });\n    };\n    SVGPainter.prototype.setBackgroundColor = function (backgroundColor) {\n        this._backgroundColor = backgroundColor;\n    };\n    SVGPainter.prototype.getSvgRoot = function () {\n        return this._mainVNode && this._mainVNode.elm;\n    };\n    SVGPainter.prototype._paintList = function (list, scope, out) {\n        var listLen = list.length;\n        var clipPathsGroupsStack = [];\n        var clipPathsGroupsStackDepth = 0;\n        var currentClipPathGroup;\n        var prevClipPaths;\n        var clipGroupNodeIdx = 0;\n        for (var i = 0; i < listLen; i++) {\n            var displayable = list[i];\n            if (!displayable.invisible) {\n                var clipPaths = displayable.__clipPaths;\n                var len = clipPaths && clipPaths.length || 0;\n                var prevLen = prevClipPaths && prevClipPaths.length || 0;\n                var lca = void 0;\n                for (lca = Math.max(len - 1, prevLen - 1); lca >= 0; lca--) {\n                    if (clipPaths && prevClipPaths\n                        && clipPaths[lca] === prevClipPaths[lca]) {\n                        break;\n                    }\n                }\n                for (var i_1 = prevLen - 1; i_1 > lca; i_1--) {\n                    clipPathsGroupsStackDepth--;\n                    currentClipPathGroup = clipPathsGroupsStack[clipPathsGroupsStackDepth - 1];\n                }\n                for (var i_2 = lca + 1; i_2 < len; i_2++) {\n                    var groupAttrs = {};\n                    setClipPath(clipPaths[i_2], groupAttrs, scope);\n                    var g = createVNode('g', 'clip-g-' + clipGroupNodeIdx++, groupAttrs, []);\n                    (currentClipPathGroup ? currentClipPathGroup.children : out).push(g);\n                    clipPathsGroupsStack[clipPathsGroupsStackDepth++] = g;\n                    currentClipPathGroup = g;\n                }\n                prevClipPaths = clipPaths;\n                var ret = brush(displayable, scope);\n                if (ret) {\n                    (currentClipPathGroup ? currentClipPathGroup.children : out).push(ret);\n                }\n            }\n        }\n    };\n    SVGPainter.prototype.resize = function (width, height) {\n        var opts = this._opts;\n        var root = this.root;\n        var viewport = this._viewport;\n        width != null && (opts.width = width);\n        height != null && (opts.height = height);\n        if (root && viewport) {\n            viewport.style.display = 'none';\n            width = getSize(root, 0, opts);\n            height = getSize(root, 1, opts);\n            viewport.style.display = '';\n        }\n        if (this._width !== width || this._height !== height) {\n            this._width = width;\n            this._height = height;\n            if (viewport) {\n                var viewportStyle = viewport.style;\n                viewportStyle.width = width + 'px';\n                viewportStyle.height = height + 'px';\n            }\n            if (!isPattern(this._backgroundColor)) {\n                var svgDom = this._svgDom;\n                if (svgDom) {\n                    svgDom.setAttribute('width', width);\n                    svgDom.setAttribute('height', height);\n                }\n                var bgEl = this._bgVNode && this._bgVNode.elm;\n                if (bgEl) {\n                    bgEl.setAttribute('width', width);\n                    bgEl.setAttribute('height', height);\n                }\n            }\n            else {\n                this.refresh();\n            }\n        }\n    };\n    SVGPainter.prototype.getWidth = function () {\n        return this._width;\n    };\n    SVGPainter.prototype.getHeight = function () {\n        return this._height;\n    };\n    SVGPainter.prototype.dispose = function () {\n        if (this.root) {\n            this.root.innerHTML = '';\n        }\n        this._svgDom =\n            this._viewport =\n                this.storage =\n                    this._oldVNode =\n                        this._bgVNode =\n                            this._mainVNode = null;\n    };\n    SVGPainter.prototype.clear = function () {\n        if (this._svgDom) {\n            this._svgDom.innerHTML = null;\n        }\n        this._oldVNode = null;\n    };\n    SVGPainter.prototype.toDataURL = function (base64) {\n        var str = this.renderToString();\n        var prefix = 'data:image/svg+xml;';\n        if (base64) {\n            str = encodeBase64(str);\n            return str && prefix + 'base64,' + str;\n        }\n        return prefix + 'charset=UTF-8,' + encodeURIComponent(str);\n    };\n    return SVGPainter;\n}());\nfunction createMethodNotSupport(method) {\n    return function () {\n        if (process.env.NODE_ENV !== 'production') {\n            logError('In SVG mode painter not support method \"' + method + '\"');\n        }\n    };\n}\nfunction createBackgroundVNode(width, height, backgroundColor, scope) {\n    var bgVNode;\n    if (backgroundColor && backgroundColor !== 'none') {\n        bgVNode = createVNode('rect', 'bg', {\n            width: width,\n            height: height,\n            x: '0',\n            y: '0'\n        });\n        if (isGradient(backgroundColor)) {\n            setGradient({ fill: backgroundColor }, bgVNode.attrs, 'fill', scope);\n        }\n        else if (isPattern(backgroundColor)) {\n            setPattern({\n                style: {\n                    fill: backgroundColor\n                },\n                dirty: noop,\n                getBoundingRect: function () { return ({ width: width, height: height }); }\n            }, bgVNode.attrs, 'fill', scope);\n        }\n        else {\n            var _a = normalizeColor(backgroundColor), color = _a.color, opacity = _a.opacity;\n            bgVNode.attrs.fill = color;\n            opacity < 1 && (bgVNode.attrs['fill-opacity'] = opacity);\n        }\n    }\n    return bgVNode;\n}\nexport default SVGPainter;\n", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport SVGPainter from 'zrender/lib/svg/Painter.js';\nexport function install(registers) {\n  registers.registerPainter('svg', SVGPainter);\n}", "import { __extends } from \"tslib\";\nimport * as util from '../core/util.js';\nimport { devicePixelRatio } from '../config.js';\nimport Eventful from '../core/Eventful.js';\nimport { getCanvasGradient } from './helper.js';\nimport { createCanvasPattern } from './graphic.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { platformApi } from '../core/platform.js';\nfunction createDom(id, painter, dpr) {\n    var newDom = platformApi.createCanvas();\n    var width = painter.getWidth();\n    var height = painter.getHeight();\n    var newDomStyle = newDom.style;\n    if (newDomStyle) {\n        newDomStyle.position = 'absolute';\n        newDomStyle.left = '0';\n        newDomStyle.top = '0';\n        newDomStyle.width = width + 'px';\n        newDomStyle.height = height + 'px';\n        newDom.setAttribute('data-zr-dom-id', id);\n    }\n    newDom.width = width * dpr;\n    newDom.height = height * dpr;\n    return newDom;\n}\n;\nvar Layer = (function (_super) {\n    __extends(Layer, _super);\n    function Layer(id, painter, dpr) {\n        var _this = _super.call(this) || this;\n        _this.motionBlur = false;\n        _this.lastFrameAlpha = 0.7;\n        _this.dpr = 1;\n        _this.virtual = false;\n        _this.config = {};\n        _this.incremental = false;\n        _this.zlevel = 0;\n        _this.maxRepaintRectCount = 5;\n        _this.__dirty = true;\n        _this.__firstTimePaint = true;\n        _this.__used = false;\n        _this.__drawIndex = 0;\n        _this.__startIndex = 0;\n        _this.__endIndex = 0;\n        _this.__prevStartIndex = null;\n        _this.__prevEndIndex = null;\n        var dom;\n        dpr = dpr || devicePixelRatio;\n        if (typeof id === 'string') {\n            dom = createDom(id, painter, dpr);\n        }\n        else if (util.isObject(id)) {\n            dom = id;\n            id = dom.id;\n        }\n        _this.id = id;\n        _this.dom = dom;\n        var domStyle = dom.style;\n        if (domStyle) {\n            util.disableUserSelect(dom);\n            dom.onselectstart = function () { return false; };\n            domStyle.padding = '0';\n            domStyle.margin = '0';\n            domStyle.borderWidth = '0';\n        }\n        _this.painter = painter;\n        _this.dpr = dpr;\n        return _this;\n    }\n    Layer.prototype.getElementCount = function () {\n        return this.__endIndex - this.__startIndex;\n    };\n    Layer.prototype.afterBrush = function () {\n        this.__prevStartIndex = this.__startIndex;\n        this.__prevEndIndex = this.__endIndex;\n    };\n    Layer.prototype.initContext = function () {\n        this.ctx = this.dom.getContext('2d');\n        this.ctx.dpr = this.dpr;\n    };\n    Layer.prototype.setUnpainted = function () {\n        this.__firstTimePaint = true;\n    };\n    Layer.prototype.createBackBuffer = function () {\n        var dpr = this.dpr;\n        this.domBack = createDom('back-' + this.id, this.painter, dpr);\n        this.ctxBack = this.domBack.getContext('2d');\n        if (dpr !== 1) {\n            this.ctxBack.scale(dpr, dpr);\n        }\n    };\n    Layer.prototype.createRepaintRects = function (displayList, prevList, viewWidth, viewHeight) {\n        if (this.__firstTimePaint) {\n            this.__firstTimePaint = false;\n            return null;\n        }\n        var mergedRepaintRects = [];\n        var maxRepaintRectCount = this.maxRepaintRectCount;\n        var full = false;\n        var pendingRect = new BoundingRect(0, 0, 0, 0);\n        function addRectToMergePool(rect) {\n            if (!rect.isFinite() || rect.isZero()) {\n                return;\n            }\n            if (mergedRepaintRects.length === 0) {\n                var boundingRect = new BoundingRect(0, 0, 0, 0);\n                boundingRect.copy(rect);\n                mergedRepaintRects.push(boundingRect);\n            }\n            else {\n                var isMerged = false;\n                var minDeltaArea = Infinity;\n                var bestRectToMergeIdx = 0;\n                for (var i = 0; i < mergedRepaintRects.length; ++i) {\n                    var mergedRect = mergedRepaintRects[i];\n                    if (mergedRect.intersect(rect)) {\n                        var pendingRect_1 = new BoundingRect(0, 0, 0, 0);\n                        pendingRect_1.copy(mergedRect);\n                        pendingRect_1.union(rect);\n                        mergedRepaintRects[i] = pendingRect_1;\n                        isMerged = true;\n                        break;\n                    }\n                    else if (full) {\n                        pendingRect.copy(rect);\n                        pendingRect.union(mergedRect);\n                        var aArea = rect.width * rect.height;\n                        var bArea = mergedRect.width * mergedRect.height;\n                        var pendingArea = pendingRect.width * pendingRect.height;\n                        var deltaArea = pendingArea - aArea - bArea;\n                        if (deltaArea < minDeltaArea) {\n                            minDeltaArea = deltaArea;\n                            bestRectToMergeIdx = i;\n                        }\n                    }\n                }\n                if (full) {\n                    mergedRepaintRects[bestRectToMergeIdx].union(rect);\n                    isMerged = true;\n                }\n                if (!isMerged) {\n                    var boundingRect = new BoundingRect(0, 0, 0, 0);\n                    boundingRect.copy(rect);\n                    mergedRepaintRects.push(boundingRect);\n                }\n                if (!full) {\n                    full = mergedRepaintRects.length >= maxRepaintRectCount;\n                }\n            }\n        }\n        for (var i = this.__startIndex; i < this.__endIndex; ++i) {\n            var el = displayList[i];\n            if (el) {\n                var shouldPaint = el.shouldBePainted(viewWidth, viewHeight, true, true);\n                var prevRect = el.__isRendered && ((el.__dirty & REDRAW_BIT) || !shouldPaint)\n                    ? el.getPrevPaintRect()\n                    : null;\n                if (prevRect) {\n                    addRectToMergePool(prevRect);\n                }\n                var curRect = shouldPaint && ((el.__dirty & REDRAW_BIT) || !el.__isRendered)\n                    ? el.getPaintRect()\n                    : null;\n                if (curRect) {\n                    addRectToMergePool(curRect);\n                }\n            }\n        }\n        for (var i = this.__prevStartIndex; i < this.__prevEndIndex; ++i) {\n            var el = prevList[i];\n            var shouldPaint = el && el.shouldBePainted(viewWidth, viewHeight, true, true);\n            if (el && (!shouldPaint || !el.__zr) && el.__isRendered) {\n                var prevRect = el.getPrevPaintRect();\n                if (prevRect) {\n                    addRectToMergePool(prevRect);\n                }\n            }\n        }\n        var hasIntersections;\n        do {\n            hasIntersections = false;\n            for (var i = 0; i < mergedRepaintRects.length;) {\n                if (mergedRepaintRects[i].isZero()) {\n                    mergedRepaintRects.splice(i, 1);\n                    continue;\n                }\n                for (var j = i + 1; j < mergedRepaintRects.length;) {\n                    if (mergedRepaintRects[i].intersect(mergedRepaintRects[j])) {\n                        hasIntersections = true;\n                        mergedRepaintRects[i].union(mergedRepaintRects[j]);\n                        mergedRepaintRects.splice(j, 1);\n                    }\n                    else {\n                        j++;\n                    }\n                }\n                i++;\n            }\n        } while (hasIntersections);\n        this._paintRects = mergedRepaintRects;\n        return mergedRepaintRects;\n    };\n    Layer.prototype.debugGetPaintRects = function () {\n        return (this._paintRects || []).slice();\n    };\n    Layer.prototype.resize = function (width, height) {\n        var dpr = this.dpr;\n        var dom = this.dom;\n        var domStyle = dom.style;\n        var domBack = this.domBack;\n        if (domStyle) {\n            domStyle.width = width + 'px';\n            domStyle.height = height + 'px';\n        }\n        dom.width = width * dpr;\n        dom.height = height * dpr;\n        if (domBack) {\n            domBack.width = width * dpr;\n            domBack.height = height * dpr;\n            if (dpr !== 1) {\n                this.ctxBack.scale(dpr, dpr);\n            }\n        }\n    };\n    Layer.prototype.clear = function (clearAll, clearColor, repaintRects) {\n        var dom = this.dom;\n        var ctx = this.ctx;\n        var width = dom.width;\n        var height = dom.height;\n        clearColor = clearColor || this.clearColor;\n        var haveMotionBLur = this.motionBlur && !clearAll;\n        var lastFrameAlpha = this.lastFrameAlpha;\n        var dpr = this.dpr;\n        var self = this;\n        if (haveMotionBLur) {\n            if (!this.domBack) {\n                this.createBackBuffer();\n            }\n            this.ctxBack.globalCompositeOperation = 'copy';\n            this.ctxBack.drawImage(dom, 0, 0, width / dpr, height / dpr);\n        }\n        var domBack = this.domBack;\n        function doClear(x, y, width, height) {\n            ctx.clearRect(x, y, width, height);\n            if (clearColor && clearColor !== 'transparent') {\n                var clearColorGradientOrPattern = void 0;\n                if (util.isGradientObject(clearColor)) {\n                    var shouldCache = clearColor.global || (clearColor.__width === width\n                        && clearColor.__height === height);\n                    clearColorGradientOrPattern = shouldCache\n                        && clearColor.__canvasGradient\n                        || getCanvasGradient(ctx, clearColor, {\n                            x: 0,\n                            y: 0,\n                            width: width,\n                            height: height\n                        });\n                    clearColor.__canvasGradient = clearColorGradientOrPattern;\n                    clearColor.__width = width;\n                    clearColor.__height = height;\n                }\n                else if (util.isImagePatternObject(clearColor)) {\n                    clearColor.scaleX = clearColor.scaleX || dpr;\n                    clearColor.scaleY = clearColor.scaleY || dpr;\n                    clearColorGradientOrPattern = createCanvasPattern(ctx, clearColor, {\n                        dirty: function () {\n                            self.setUnpainted();\n                            self.painter.refresh();\n                        }\n                    });\n                }\n                ctx.save();\n                ctx.fillStyle = clearColorGradientOrPattern || clearColor;\n                ctx.fillRect(x, y, width, height);\n                ctx.restore();\n            }\n            if (haveMotionBLur) {\n                ctx.save();\n                ctx.globalAlpha = lastFrameAlpha;\n                ctx.drawImage(domBack, x, y, width, height);\n                ctx.restore();\n            }\n        }\n        ;\n        if (!repaintRects || haveMotionBLur) {\n            doClear(0, 0, width, height);\n        }\n        else if (repaintRects.length) {\n            util.each(repaintRects, function (rect) {\n                doClear(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n            });\n        }\n    };\n    return Layer;\n}(Eventful));\nexport default Layer;\n", "import { devicePixelRatio } from '../config.js';\nimport * as util from '../core/util.js';\nimport Layer from './Layer.js';\nimport requestAnimationFrame from '../animation/requestAnimationFrame.js';\nimport env from '../core/env.js';\nimport { brush, brushSingle } from './graphic.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { getSize } from './helper.js';\nvar HOVER_LAYER_ZLEVEL = 1e5;\nvar CANVAS_ZLEVEL = 314159;\nvar EL_AFTER_INCREMENTAL_INC = 0.01;\nvar INCREMENTAL_INC = 0.001;\nfunction isLayerValid(layer) {\n    if (!layer) {\n        return false;\n    }\n    if (layer.__builtin__) {\n        return true;\n    }\n    if (typeof (layer.resize) !== 'function'\n        || typeof (layer.refresh) !== 'function') {\n        return false;\n    }\n    return true;\n}\nfunction createRoot(width, height) {\n    var domRoot = document.createElement('div');\n    domRoot.style.cssText = [\n        'position:relative',\n        'width:' + width + 'px',\n        'height:' + height + 'px',\n        'padding:0',\n        'margin:0',\n        'border-width:0'\n    ].join(';') + ';';\n    return domRoot;\n}\nvar CanvasPainter = (function () {\n    function CanvasPainter(root, storage, opts, id) {\n        this.type = 'canvas';\n        this._zlevelList = [];\n        this._prevDisplayList = [];\n        this._layers = {};\n        this._layerConfig = {};\n        this._needsManuallyCompositing = false;\n        this.type = 'canvas';\n        var singleCanvas = !root.nodeName\n            || root.nodeName.toUpperCase() === 'CANVAS';\n        this._opts = opts = util.extend({}, opts || {});\n        this.dpr = opts.devicePixelRatio || devicePixelRatio;\n        this._singleCanvas = singleCanvas;\n        this.root = root;\n        var rootStyle = root.style;\n        if (rootStyle) {\n            util.disableUserSelect(root);\n            root.innerHTML = '';\n        }\n        this.storage = storage;\n        var zlevelList = this._zlevelList;\n        this._prevDisplayList = [];\n        var layers = this._layers;\n        if (!singleCanvas) {\n            this._width = getSize(root, 0, opts);\n            this._height = getSize(root, 1, opts);\n            var domRoot = this._domRoot = createRoot(this._width, this._height);\n            root.appendChild(domRoot);\n        }\n        else {\n            var rootCanvas = root;\n            var width = rootCanvas.width;\n            var height = rootCanvas.height;\n            if (opts.width != null) {\n                width = opts.width;\n            }\n            if (opts.height != null) {\n                height = opts.height;\n            }\n            this.dpr = opts.devicePixelRatio || 1;\n            rootCanvas.width = width * this.dpr;\n            rootCanvas.height = height * this.dpr;\n            this._width = width;\n            this._height = height;\n            var mainLayer = new Layer(rootCanvas, this, this.dpr);\n            mainLayer.__builtin__ = true;\n            mainLayer.initContext();\n            layers[CANVAS_ZLEVEL] = mainLayer;\n            mainLayer.zlevel = CANVAS_ZLEVEL;\n            zlevelList.push(CANVAS_ZLEVEL);\n            this._domRoot = root;\n        }\n    }\n    CanvasPainter.prototype.getType = function () {\n        return 'canvas';\n    };\n    CanvasPainter.prototype.isSingleCanvas = function () {\n        return this._singleCanvas;\n    };\n    CanvasPainter.prototype.getViewportRoot = function () {\n        return this._domRoot;\n    };\n    CanvasPainter.prototype.getViewportRootOffset = function () {\n        var viewportRoot = this.getViewportRoot();\n        if (viewportRoot) {\n            return {\n                offsetLeft: viewportRoot.offsetLeft || 0,\n                offsetTop: viewportRoot.offsetTop || 0\n            };\n        }\n    };\n    CanvasPainter.prototype.refresh = function (paintAll) {\n        var list = this.storage.getDisplayList(true);\n        var prevList = this._prevDisplayList;\n        var zlevelList = this._zlevelList;\n        this._redrawId = Math.random();\n        this._paintList(list, prevList, paintAll, this._redrawId);\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            var layer = this._layers[z];\n            if (!layer.__builtin__ && layer.refresh) {\n                var clearColor = i === 0 ? this._backgroundColor : null;\n                layer.refresh(clearColor);\n            }\n        }\n        if (this._opts.useDirtyRect) {\n            this._prevDisplayList = list.slice();\n        }\n        return this;\n    };\n    CanvasPainter.prototype.refreshHover = function () {\n        this._paintHoverList(this.storage.getDisplayList(false));\n    };\n    CanvasPainter.prototype._paintHoverList = function (list) {\n        var len = list.length;\n        var hoverLayer = this._hoverlayer;\n        hoverLayer && hoverLayer.clear();\n        if (!len) {\n            return;\n        }\n        var scope = {\n            inHover: true,\n            viewWidth: this._width,\n            viewHeight: this._height\n        };\n        var ctx;\n        for (var i = 0; i < len; i++) {\n            var el = list[i];\n            if (el.__inHover) {\n                if (!hoverLayer) {\n                    hoverLayer = this._hoverlayer = this.getLayer(HOVER_LAYER_ZLEVEL);\n                }\n                if (!ctx) {\n                    ctx = hoverLayer.ctx;\n                    ctx.save();\n                }\n                brush(ctx, el, scope, i === len - 1);\n            }\n        }\n        if (ctx) {\n            ctx.restore();\n        }\n    };\n    CanvasPainter.prototype.getHoverLayer = function () {\n        return this.getLayer(HOVER_LAYER_ZLEVEL);\n    };\n    CanvasPainter.prototype.paintOne = function (ctx, el) {\n        brushSingle(ctx, el);\n    };\n    CanvasPainter.prototype._paintList = function (list, prevList, paintAll, redrawId) {\n        if (this._redrawId !== redrawId) {\n            return;\n        }\n        paintAll = paintAll || false;\n        this._updateLayerStatus(list);\n        var _a = this._doPaintList(list, prevList, paintAll), finished = _a.finished, needsRefreshHover = _a.needsRefreshHover;\n        if (this._needsManuallyCompositing) {\n            this._compositeManually();\n        }\n        if (needsRefreshHover) {\n            this._paintHoverList(list);\n        }\n        if (!finished) {\n            var self_1 = this;\n            requestAnimationFrame(function () {\n                self_1._paintList(list, prevList, paintAll, redrawId);\n            });\n        }\n        else {\n            this.eachLayer(function (layer) {\n                layer.afterBrush && layer.afterBrush();\n            });\n        }\n    };\n    CanvasPainter.prototype._compositeManually = function () {\n        var ctx = this.getLayer(CANVAS_ZLEVEL).ctx;\n        var width = this._domRoot.width;\n        var height = this._domRoot.height;\n        ctx.clearRect(0, 0, width, height);\n        this.eachBuiltinLayer(function (layer) {\n            if (layer.virtual) {\n                ctx.drawImage(layer.dom, 0, 0, width, height);\n            }\n        });\n    };\n    CanvasPainter.prototype._doPaintList = function (list, prevList, paintAll) {\n        var _this = this;\n        var layerList = [];\n        var useDirtyRect = this._opts.useDirtyRect;\n        for (var zi = 0; zi < this._zlevelList.length; zi++) {\n            var zlevel = this._zlevelList[zi];\n            var layer = this._layers[zlevel];\n            if (layer.__builtin__\n                && layer !== this._hoverlayer\n                && (layer.__dirty || paintAll)) {\n                layerList.push(layer);\n            }\n        }\n        var finished = true;\n        var needsRefreshHover = false;\n        var _loop_1 = function (k) {\n            var layer = layerList[k];\n            var ctx = layer.ctx;\n            var repaintRects = useDirtyRect\n                && layer.createRepaintRects(list, prevList, this_1._width, this_1._height);\n            var start = paintAll ? layer.__startIndex : layer.__drawIndex;\n            var useTimer = !paintAll && layer.incremental && Date.now;\n            var startTime = useTimer && Date.now();\n            var clearColor = layer.zlevel === this_1._zlevelList[0]\n                ? this_1._backgroundColor : null;\n            if (layer.__startIndex === layer.__endIndex) {\n                layer.clear(false, clearColor, repaintRects);\n            }\n            else if (start === layer.__startIndex) {\n                var firstEl = list[start];\n                if (!firstEl.incremental || !firstEl.notClear || paintAll) {\n                    layer.clear(false, clearColor, repaintRects);\n                }\n            }\n            if (start === -1) {\n                console.error('For some unknown reason. drawIndex is -1');\n                start = layer.__startIndex;\n            }\n            var i;\n            var repaint = function (repaintRect) {\n                var scope = {\n                    inHover: false,\n                    allClipped: false,\n                    prevEl: null,\n                    viewWidth: _this._width,\n                    viewHeight: _this._height\n                };\n                for (i = start; i < layer.__endIndex; i++) {\n                    var el = list[i];\n                    if (el.__inHover) {\n                        needsRefreshHover = true;\n                    }\n                    _this._doPaintEl(el, layer, useDirtyRect, repaintRect, scope, i === layer.__endIndex - 1);\n                    if (useTimer) {\n                        var dTime = Date.now() - startTime;\n                        if (dTime > 15) {\n                            break;\n                        }\n                    }\n                }\n                if (scope.prevElClipPaths) {\n                    ctx.restore();\n                }\n            };\n            if (repaintRects) {\n                if (repaintRects.length === 0) {\n                    i = layer.__endIndex;\n                }\n                else {\n                    var dpr = this_1.dpr;\n                    for (var r = 0; r < repaintRects.length; ++r) {\n                        var rect = repaintRects[r];\n                        ctx.save();\n                        ctx.beginPath();\n                        ctx.rect(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n                        ctx.clip();\n                        repaint(rect);\n                        ctx.restore();\n                    }\n                }\n            }\n            else {\n                ctx.save();\n                repaint();\n                ctx.restore();\n            }\n            layer.__drawIndex = i;\n            if (layer.__drawIndex < layer.__endIndex) {\n                finished = false;\n            }\n        };\n        var this_1 = this;\n        for (var k = 0; k < layerList.length; k++) {\n            _loop_1(k);\n        }\n        if (env.wxa) {\n            util.each(this._layers, function (layer) {\n                if (layer && layer.ctx && layer.ctx.draw) {\n                    layer.ctx.draw();\n                }\n            });\n        }\n        return {\n            finished: finished,\n            needsRefreshHover: needsRefreshHover\n        };\n    };\n    CanvasPainter.prototype._doPaintEl = function (el, currentLayer, useDirtyRect, repaintRect, scope, isLast) {\n        var ctx = currentLayer.ctx;\n        if (useDirtyRect) {\n            var paintRect = el.getPaintRect();\n            if (!repaintRect || paintRect && paintRect.intersect(repaintRect)) {\n                brush(ctx, el, scope, isLast);\n                el.setPrevPaintRect(paintRect);\n            }\n        }\n        else {\n            brush(ctx, el, scope, isLast);\n        }\n    };\n    CanvasPainter.prototype.getLayer = function (zlevel, virtual) {\n        if (this._singleCanvas && !this._needsManuallyCompositing) {\n            zlevel = CANVAS_ZLEVEL;\n        }\n        var layer = this._layers[zlevel];\n        if (!layer) {\n            layer = new Layer('zr_' + zlevel, this, this.dpr);\n            layer.zlevel = zlevel;\n            layer.__builtin__ = true;\n            if (this._layerConfig[zlevel]) {\n                util.merge(layer, this._layerConfig[zlevel], true);\n            }\n            else if (this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC]) {\n                util.merge(layer, this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC], true);\n            }\n            if (virtual) {\n                layer.virtual = virtual;\n            }\n            this.insertLayer(zlevel, layer);\n            layer.initContext();\n        }\n        return layer;\n    };\n    CanvasPainter.prototype.insertLayer = function (zlevel, layer) {\n        var layersMap = this._layers;\n        var zlevelList = this._zlevelList;\n        var len = zlevelList.length;\n        var domRoot = this._domRoot;\n        var prevLayer = null;\n        var i = -1;\n        if (layersMap[zlevel]) {\n            if (process.env.NODE_ENV !== 'production') {\n                util.logError('ZLevel ' + zlevel + ' has been used already');\n            }\n            return;\n        }\n        if (!isLayerValid(layer)) {\n            if (process.env.NODE_ENV !== 'production') {\n                util.logError('Layer of zlevel ' + zlevel + ' is not valid');\n            }\n            return;\n        }\n        if (len > 0 && zlevel > zlevelList[0]) {\n            for (i = 0; i < len - 1; i++) {\n                if (zlevelList[i] < zlevel\n                    && zlevelList[i + 1] > zlevel) {\n                    break;\n                }\n            }\n            prevLayer = layersMap[zlevelList[i]];\n        }\n        zlevelList.splice(i + 1, 0, zlevel);\n        layersMap[zlevel] = layer;\n        if (!layer.virtual) {\n            if (prevLayer) {\n                var prevDom = prevLayer.dom;\n                if (prevDom.nextSibling) {\n                    domRoot.insertBefore(layer.dom, prevDom.nextSibling);\n                }\n                else {\n                    domRoot.appendChild(layer.dom);\n                }\n            }\n            else {\n                if (domRoot.firstChild) {\n                    domRoot.insertBefore(layer.dom, domRoot.firstChild);\n                }\n                else {\n                    domRoot.appendChild(layer.dom);\n                }\n            }\n        }\n        layer.painter || (layer.painter = this);\n    };\n    CanvasPainter.prototype.eachLayer = function (cb, context) {\n        var zlevelList = this._zlevelList;\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            cb.call(context, this._layers[z], z);\n        }\n    };\n    CanvasPainter.prototype.eachBuiltinLayer = function (cb, context) {\n        var zlevelList = this._zlevelList;\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            var layer = this._layers[z];\n            if (layer.__builtin__) {\n                cb.call(context, layer, z);\n            }\n        }\n    };\n    CanvasPainter.prototype.eachOtherLayer = function (cb, context) {\n        var zlevelList = this._zlevelList;\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            var layer = this._layers[z];\n            if (!layer.__builtin__) {\n                cb.call(context, layer, z);\n            }\n        }\n    };\n    CanvasPainter.prototype.getLayers = function () {\n        return this._layers;\n    };\n    CanvasPainter.prototype._updateLayerStatus = function (list) {\n        this.eachBuiltinLayer(function (layer, z) {\n            layer.__dirty = layer.__used = false;\n        });\n        function updatePrevLayer(idx) {\n            if (prevLayer) {\n                if (prevLayer.__endIndex !== idx) {\n                    prevLayer.__dirty = true;\n                }\n                prevLayer.__endIndex = idx;\n            }\n        }\n        if (this._singleCanvas) {\n            for (var i_1 = 1; i_1 < list.length; i_1++) {\n                var el = list[i_1];\n                if (el.zlevel !== list[i_1 - 1].zlevel || el.incremental) {\n                    this._needsManuallyCompositing = true;\n                    break;\n                }\n            }\n        }\n        var prevLayer = null;\n        var incrementalLayerCount = 0;\n        var prevZlevel;\n        var i;\n        for (i = 0; i < list.length; i++) {\n            var el = list[i];\n            var zlevel = el.zlevel;\n            var layer = void 0;\n            if (prevZlevel !== zlevel) {\n                prevZlevel = zlevel;\n                incrementalLayerCount = 0;\n            }\n            if (el.incremental) {\n                layer = this.getLayer(zlevel + INCREMENTAL_INC, this._needsManuallyCompositing);\n                layer.incremental = true;\n                incrementalLayerCount = 1;\n            }\n            else {\n                layer = this.getLayer(zlevel + (incrementalLayerCount > 0 ? EL_AFTER_INCREMENTAL_INC : 0), this._needsManuallyCompositing);\n            }\n            if (!layer.__builtin__) {\n                util.logError('ZLevel ' + zlevel + ' has been used by unkown layer ' + layer.id);\n            }\n            if (layer !== prevLayer) {\n                layer.__used = true;\n                if (layer.__startIndex !== i) {\n                    layer.__dirty = true;\n                }\n                layer.__startIndex = i;\n                if (!layer.incremental) {\n                    layer.__drawIndex = i;\n                }\n                else {\n                    layer.__drawIndex = -1;\n                }\n                updatePrevLayer(i);\n                prevLayer = layer;\n            }\n            if ((el.__dirty & REDRAW_BIT) && !el.__inHover) {\n                layer.__dirty = true;\n                if (layer.incremental && layer.__drawIndex < 0) {\n                    layer.__drawIndex = i;\n                }\n            }\n        }\n        updatePrevLayer(i);\n        this.eachBuiltinLayer(function (layer, z) {\n            if (!layer.__used && layer.getElementCount() > 0) {\n                layer.__dirty = true;\n                layer.__startIndex = layer.__endIndex = layer.__drawIndex = 0;\n            }\n            if (layer.__dirty && layer.__drawIndex < 0) {\n                layer.__drawIndex = layer.__startIndex;\n            }\n        });\n    };\n    CanvasPainter.prototype.clear = function () {\n        this.eachBuiltinLayer(this._clearLayer);\n        return this;\n    };\n    CanvasPainter.prototype._clearLayer = function (layer) {\n        layer.clear();\n    };\n    CanvasPainter.prototype.setBackgroundColor = function (backgroundColor) {\n        this._backgroundColor = backgroundColor;\n        util.each(this._layers, function (layer) {\n            layer.setUnpainted();\n        });\n    };\n    CanvasPainter.prototype.configLayer = function (zlevel, config) {\n        if (config) {\n            var layerConfig = this._layerConfig;\n            if (!layerConfig[zlevel]) {\n                layerConfig[zlevel] = config;\n            }\n            else {\n                util.merge(layerConfig[zlevel], config, true);\n            }\n            for (var i = 0; i < this._zlevelList.length; i++) {\n                var _zlevel = this._zlevelList[i];\n                if (_zlevel === zlevel || _zlevel === zlevel + EL_AFTER_INCREMENTAL_INC) {\n                    var layer = this._layers[_zlevel];\n                    util.merge(layer, layerConfig[zlevel], true);\n                }\n            }\n        }\n    };\n    CanvasPainter.prototype.delLayer = function (zlevel) {\n        var layers = this._layers;\n        var zlevelList = this._zlevelList;\n        var layer = layers[zlevel];\n        if (!layer) {\n            return;\n        }\n        layer.dom.parentNode.removeChild(layer.dom);\n        delete layers[zlevel];\n        zlevelList.splice(util.indexOf(zlevelList, zlevel), 1);\n    };\n    CanvasPainter.prototype.resize = function (width, height) {\n        if (!this._domRoot.style) {\n            if (width == null || height == null) {\n                return;\n            }\n            this._width = width;\n            this._height = height;\n            this.getLayer(CANVAS_ZLEVEL).resize(width, height);\n        }\n        else {\n            var domRoot = this._domRoot;\n            domRoot.style.display = 'none';\n            var opts = this._opts;\n            var root = this.root;\n            width != null && (opts.width = width);\n            height != null && (opts.height = height);\n            width = getSize(root, 0, opts);\n            height = getSize(root, 1, opts);\n            domRoot.style.display = '';\n            if (this._width !== width || height !== this._height) {\n                domRoot.style.width = width + 'px';\n                domRoot.style.height = height + 'px';\n                for (var id in this._layers) {\n                    if (this._layers.hasOwnProperty(id)) {\n                        this._layers[id].resize(width, height);\n                    }\n                }\n                this.refresh(true);\n            }\n            this._width = width;\n            this._height = height;\n        }\n        return this;\n    };\n    CanvasPainter.prototype.clearLayer = function (zlevel) {\n        var layer = this._layers[zlevel];\n        if (layer) {\n            layer.clear();\n        }\n    };\n    CanvasPainter.prototype.dispose = function () {\n        this.root.innerHTML = '';\n        this.root =\n            this.storage =\n                this._domRoot =\n                    this._layers = null;\n    };\n    CanvasPainter.prototype.getRenderedCanvas = function (opts) {\n        opts = opts || {};\n        if (this._singleCanvas && !this._compositeManually) {\n            return this._layers[CANVAS_ZLEVEL].dom;\n        }\n        var imageLayer = new Layer('image', this, opts.pixelRatio || this.dpr);\n        imageLayer.initContext();\n        imageLayer.clear(false, opts.backgroundColor || this._backgroundColor);\n        var ctx = imageLayer.ctx;\n        if (opts.pixelRatio <= this.dpr) {\n            this.refresh();\n            var width_1 = imageLayer.dom.width;\n            var height_1 = imageLayer.dom.height;\n            this.eachLayer(function (layer) {\n                if (layer.__builtin__) {\n                    ctx.drawImage(layer.dom, 0, 0, width_1, height_1);\n                }\n                else if (layer.renderToCanvas) {\n                    ctx.save();\n                    layer.renderToCanvas(ctx);\n                    ctx.restore();\n                }\n            });\n        }\n        else {\n            var scope = {\n                inHover: false,\n                viewWidth: this._width,\n                viewHeight: this._height\n            };\n            var displayList = this.storage.getDisplayList(true);\n            for (var i = 0, len = displayList.length; i < len; i++) {\n                var el = displayList[i];\n                brush(ctx, el, scope, i === len - 1);\n            }\n        }\n        return imageLayer.dom;\n    };\n    CanvasPainter.prototype.getWidth = function () {\n        return this._width;\n    };\n    CanvasPainter.prototype.getHeight = function () {\n        return this._height;\n    };\n    return CanvasPainter;\n}());\nexport default CanvasPainter;\n;\n", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport CanvasPainter from 'zrender/lib/canvas/Painter.js';\nexport function install(registers) {\n  registers.registerPainter('canvas', CanvasPainter);\n}"], "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], "mappings": ";;;AACA,IAAI,UAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI,KAAK,KAAK;AACd,IAAI,MAAM,KAAK,KAAK;AACpB,IAAI,SAAS,MAAM;AACnB,IAAI,mBAAoB,WAAY;CAChC,SAASA,qBAAmB,CAC3B;AACD,oBAAiB,UAAU,QAAQ,SAAU,WAAW;AACpD,OAAK,SAAS;AACd,OAAK,KAAK,CAAE;AACZ,OAAK,OAAO;AACZ,OAAK,KAAK,KAAK,IAAI,IAAI,aAAa,EAAE;CACzC;AACD,oBAAiB,UAAU,SAAS,SAAU,GAAG,GAAG;AAChD,OAAK,KAAK,KAAK,GAAG,EAAE;CACvB;AACD,oBAAiB,UAAU,SAAS,SAAU,GAAG,GAAG;AAChD,OAAK,KAAK,KAAK,GAAG,EAAE;CACvB;AACD,oBAAiB,UAAU,gBAAgB,SAAU,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI;AACvE,OAAK,KAAK,KAAK,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG;CACvC;AACD,oBAAiB,UAAU,mBAAmB,SAAU,GAAG,GAAG,IAAI,IAAI;AAClE,OAAK,KAAK,KAAK,GAAG,GAAG,IAAI,GAAG;CAC/B;AACD,oBAAiB,UAAU,MAAM,SAAU,IAAI,IAAI,GAAG,YAAY,UAAU,eAAe;AACvF,OAAK,QAAQ,IAAI,IAAI,GAAG,GAAG,GAAG,YAAY,UAAU,cAAc;CACrE;AACD,oBAAiB,UAAU,UAAU,SAAU,IAAI,IAAI,IAAI,IAAI,KAAK,YAAY,UAAU,eAAe;EACrG,IAAI,SAAS,WAAW;EACxB,IAAI,aAAa;EACjB,IAAI,iBAAiB,KAAK,IAAI,OAAO;EACrC,IAAI,WAAW,aAAa,iBAAiB,IAAI,KACzC,YAAY,UAAU,OAAO,UAAU;EAC/C,IAAI,eAAe,SAAS,IAAI,SAAS,MAAO,SAAS,MAAM;EAC/D,IAAI,QAAQ;AACZ,MAAI,SACA,SAAQ;WAEH,aAAa,eAAe,CACjC,SAAQ;MAGR,SAAS,gBAAgB,SAAU;EAEvC,IAAI,KAAK,KAAK,KAAK,QAAQ,WAAW;EACtC,IAAI,KAAK,KAAK,KAAK,QAAQ,WAAW;AACtC,MAAI,KAAK,OACL,MAAK,KAAK,KAAK,IAAI,GAAG;EAE1B,IAAI,OAAO,KAAK,MAAM,MAAM,OAAO;AACnC,MAAI,UAAU;GACV,IAAI,IAAI,IAAI,KAAK;GACjB,IAAI,YAAY,YAAY,IAAI,OAAO,MAAM;AAC7C,QAAK,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,WAAW,KAAK,KAAK,QAAQ,aAAa,SAAS,EAAE,KAAK,KAAK,QAAQ,aAAa,SAAS,CAAC;AAC/H,OAAI,IAAI,IACJ,MAAK,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,WAAW,IAAI,GAAG;EAE1D,OACI;GACD,IAAI,IAAI,KAAK,KAAK,QAAQ,SAAS;GACnC,IAAI,IAAI,KAAK,KAAK,QAAQ,SAAS;AACnC,QAAK,KAAK,KAAK,IAAI,IAAI,OAAO,QAAQ,WAAW,GAAG,EAAE;EACzD;CACJ;AACD,oBAAiB,UAAU,OAAO,SAAU,GAAG,GAAG,GAAG,GAAG;AACpD,OAAK,KAAK,KAAK,GAAG,EAAE;AACpB,OAAK,KAAK,KAAK,GAAG,EAAE;AACpB,OAAK,KAAK,KAAK,GAAG,EAAE;AACpB,OAAK,KAAK,MAAM,GAAG,EAAE;AACrB,OAAK,KAAK,IAAI;CACjB;AACD,oBAAiB,UAAU,YAAY,WAAY;AAC/C,MAAI,KAAK,GAAG,SAAS,EACjB,MAAK,KAAK,IAAI;CAErB;AACD,oBAAiB,UAAU,OAAO,SAAU,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;EACrE,IAAI,OAAO,CAAE;EACb,IAAI,IAAI,KAAK;AACb,OAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;GACvC,IAAI,MAAM,UAAU;AACpB,OAAI,MAAM,IAAI,EAAE;AACZ,SAAK,WAAW;AAChB;GACH;AACD,QAAK,KAAK,KAAK,MAAM,MAAM,EAAE,GAAG,EAAE;EACrC;AACD,OAAK,GAAG,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC;AAClC,OAAK,SAAS,QAAQ;CACzB;AACD,oBAAiB,UAAU,cAAc,WAAY;AACjD,OAAK,OAAO,KAAK,WAAW,KAAK,KAAK,GAAG,KAAK,GAAG;AACjD,OAAK,KAAK,CAAE;CACf;AACD,oBAAiB,UAAU,SAAS,WAAY;AAC5C,SAAO,KAAK;CACf;AACD,QAAOA;AACV,GAAE;AACH,+BAAe;;;;ACjGf,IAAI,OAAO;AACX,IAAI,YAAY,KAAK;AACrB,SAAS,YAAY,OAAO;CACxB,IAAI,OAAO,MAAM;AACjB,QAAO,QAAQ,QAAQ,SAAS;AACnC;AACD,SAAS,cAAc,OAAO;CAC1B,IAAI,SAAS,MAAM;AACnB,QAAO,UAAU,QAAQ,WAAW;AACvC;AACD,IAAI,cAAc;CAAC;CAAW;CAAc;AAAW;AACvD,IAAI,iBAAiB,IAAI,aAAa,SAAU,MAAM;AAAE,QAAO,YAAY,KAAK,aAAa;AAAG,EAAC;AACjG,SAAwB,gBAAgB,YAAY,OAAO,IAAI,aAAa;CACxE,IAAI,UAAU,MAAM,WAAW,OAAO,IAAI,MAAM;AAChD,KAAI,cAAcC,eAAS;AACvB,aAAW,WAAW,QAAQ;AAC9B;CACH;AACD,KAAI,YAAY,MAAM,EAAE;EACpB,IAAI,OAAO,eAAe,MAAM,KAAK;AACrC,aAAW,QAAQ,KAAK,MAAM;EAC9B,IAAI,cAAc,MAAM,eAAe,OACjC,MAAM,cAAc,KAAK,UAAU,UACnC,KAAK,UAAU;AACrB,MAAI,eAAe,cAAc,EAC7B,YAAW,gBAAgB,YAAY;CAE9C,MAEG,YAAW,QAAQ,KAAK;AAE5B,KAAI,cAAc,MAAM,EAAE;EACtB,IAAI,SAAS,eAAe,MAAM,OAAO;AACzC,aAAW,UAAU,OAAO,MAAM;EAClC,IAAI,cAAc,MAAM,gBAClB,GAAG,cAAc,GACjB;EACN,IAAI,cAAe,eAAe,MAAM,aAAa,KAAK,cAAc;EACxE,IAAI,gBAAgB,MAAM,iBAAiB,OACrC,MAAM,gBAAgB,OAAO,UAAU,UACvC,OAAO,UAAU;EACvB,IAAI,cAAc,MAAM;AACxB,MAAI,eAAe,gBAAgB,EAC/B,YAAW,gBAAgB,YAAY;AAE3C,MAAI,eAAe,YACf,YAAW,eAAe,cAAc,WAAW,OAAO;AAE9D,MAAI,eAAe,gBAAgB,EAC/B,YAAW,kBAAkB,cAAc;AAE/C,MAAI,MAAM,UAAU;GAChB,IAAI,KAAK,YAAY,GAAG,EAAE,WAAW,GAAG,IAAI,iBAAiB,GAAG;AAChE,OAAI,UAAU;AACV,qBAAiB,UAAU,kBAAkB,EAAE;AAC/C,eAAW,oBAAoB,SAAS,KAAK,IAAI,CAAC;AAClD,QAAI,kBAAkB,YAClB,YAAW,qBAAqB,eAAe;GAEtD;EACJ,WACQ,YACL,YAAW,oBAAoB,KAAK;AAExC,OAAK,IAAI,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;GACzC,IAAI,WAAW,YAAY;AAC3B,OAAI,eAAe,MAAM,cAAc,mBAAmB,WAAW;IACjE,IAAI,MAAM,MAAM,aAAa,mBAAmB;AAChD,WAAO,WAAW,eAAe,IAAI,IAAI;GAC5C;EACJ;CACJ,WACQ,YACL,YAAW,UAAU,KAAK;AAEjC;;;;AC9ED,IAAW,QAAQ;AACnB,IAAW,UAAU;AACrB,IAAW,QAAQ;AACnB,IAAW,gBAAgB;AAC3B,IAAW,mBAAmB;AAC9B,SAAgB,cAAc,MAAM;AAChC,QAAO,SAAS,gBAAgB,OAAO,KAAK;AAC/C;AAED,SAAgB,YAAY,KAAK,KAAK,OAAO,UAAU,MAAM;AACzD,QAAO;EACE;EACL,OAAO,SAAS,CAAE;EACR;EACJ;EACD;CACR;AACJ;AACD,SAAS,kBAAkB,MAAM,OAAO;CACpC,IAAI,WAAW,CAAE;AACjB,KAAI,MACA,MAAK,IAAI,OAAO,OAAO;EACnB,IAAI,MAAM,MAAM;EAChB,IAAI,OAAO;AACX,MAAI,QAAQ,MACR;WAEK,QAAQ,QAAQ,OAAO,KAC5B,SAAQ,QAAQ,MAAM;AAE1B,WAAS,KAAK,KAAK;CACtB;AAEL,QAAO,MAAM,OAAO,MAAM,SAAS,KAAK,IAAI,GAAG;AAClD;AACD,SAAS,mBAAmB,MAAM;AAC9B,QAAO,OAAO,OAAO;AACxB;AACD,SAAgB,cAAc,IAAI,MAAM;AACpC,QAAO,QAAQ,CAAE;CACjB,IAAI,IAAI,KAAK,UAAU,OAAO;CAC9B,SAAS,kBAAkBC,MAAI;EAC3B,IAAI,WAAWA,KAAG,UAAU,MAAMA,KAAG,KAAK,QAAQA,KAAG,OAAO,OAAOA,KAAG;AACtE,SAAO,kBAAkB,KAAK,MAAM,IAC7B,QAAQ,UAAU,WAAW,KAAK,GAAG,QAAQ,OAC7C,WAAW,KAAK,IAAI,IAAI,UAAU,SAAU,OAAO;AAAE,UAAO,kBAAkB,MAAM;EAAG,EAAC,CAAC,KAAK,EAAE,GAAG,IAAI,MACxG,mBAAmB,IAAI;CAChC;AACD,QAAO,kBAAkB,GAAG;AAC/B;AACD,SAAgB,aAAa,eAAe,gBAAgB,MAAM;AAC9D,QAAO,QAAQ,CAAE;CACjB,IAAI,IAAI,KAAK,UAAU,OAAO;CAC9B,IAAI,eAAe,OAAO;CAC1B,IAAI,aAAa,IAAI;CACrB,IAAI,YAAY,IAAI,KAAK,cAAc,EAAE,SAAU,WAAW;AAC1D,SAAO,YAAY,eAAe,IAAI,KAAK,cAAc,WAAW,EAAE,SAAU,UAAU;AACtF,UAAO,WAAW,MAAM,cAAc,WAAW,YAAY;EAChE,EAAC,CAAC,KAAK,EAAE,GAAG;CAChB,EAAC,CAAC,KAAK,EAAE;CACV,IAAI,aAAa,IAAI,KAAK,eAAe,EAAE,SAAU,eAAe;AAChE,SAAO,gBAAgB,gBAAgB,eAAe,IAAI,KAAK,eAAe,eAAe,EAAE,SAAU,SAAS;AAC9G,UAAO,UAAU,eAAe,IAAI,KAAK,eAAe,eAAe,SAAS,EAAE,SAAU,UAAU;IAClG,IAAI,MAAM,eAAe,eAAe,SAAS;AACjD,QAAI,aAAa,IACb,OAAM,YAAY,MAAM;AAE5B,WAAO,WAAW,MAAM,MAAM;GACjC,EAAC,CAAC,KAAK,EAAE,GAAG;EAChB,EAAC,CAAC,KAAK,EAAE,GAAG;CAChB,EAAC,CAAC,KAAK,EAAE;AACV,MAAK,cAAc,WACf,QAAO;AAEX,QAAO;EAAC;EAAa;EAAW;EAAY;CAAM,EAAC,KAAK,EAAE;AAC7D;AACD,SAAgB,iBAAiB,MAAM;AACnC,QAAO;EACG;EACN,aAAa,CAAE;EACf,cAAc,CAAE;EAChB,eAAe,CAAE;EACjB,eAAe,CAAE;EACjB,MAAM,CAAE;EACR,UAAU,CAAE;EACZ,UAAU,CAAE;EACZ,eAAe,CAAE;EACjB,YAAY;EACZ,WAAW;EACX,aAAa;EACb,YAAY;EACZ,aAAa;CAChB;AACJ;AACD,SAAgB,eAAe,OAAO,QAAQ,UAAU,YAAY;AAChE,QAAO,YAAY,OAAO,QAAQ;EAC9B,SAAS;EACT,UAAU;EACV,SAAS;EACT,eAAe;EACf,WAAW;EACX,eAAe;EACf,WAAW,aAAa,SAAS,QAAQ,MAAM,SAAS;CAC3D,GAAE,SAAS;AACf;;;;AC1GD,IAAI,cAAc;AAClB,SAAgB,aAAa;AACzB,QAAO;AACV;;;;ACMD,IAAW,aAAa;CACpB,SAAS;CACT,UAAU;CACV,YAAY;CACZ,aAAa;CACb,cAAc;CACd,gBAAgB;CAChB,WAAW;CACX,YAAY;CACZ,cAAc;CACd,WAAW;CACX,YAAY;CACZ,cAAc;CACd,cAAc;CACd,eAAe;CACf,iBAAiB;CACjB,eAAe;CACf,gBAAgB;CAChB,kBAAkB;CAClB,YAAY;CACZ,aAAa;CACb,eAAe;AAClB;AACD,IAAI,qBAAqB;AACzB,SAAS,gBAAgB,IAAI,SAAS,MAAM;CACxC,IAAI,QAAQ,OAAO,CAAE,GAAE,GAAG,MAAM;AAChC,QAAO,OAAO,QAAQ;AACtB,IAAG,UAAU,MAAM,MAAM;CACzB,IAAI,iBAAiB,IAAIC;AACzB,gBAAe,MAAM,iBAAiB,GAAG,CAAC;AAC1C,MAAK,YAAY,gBAAgB,EAAE;AACnC,gBAAe,aAAa;AAC5B,QAAO,eAAe,QAAQ;AACjC;AACD,SAAS,mBAAmB,QAAQ,WAAW;CAC3C,IAAI,UAAU,UAAU,SAAS,UAAU,UAAU;AACrD,KAAI,WAAW,QACX,QAAO,sBAAsB,UAAU,QAAQ,UAAU;AAEhE;AACD,IAAW,oBAAoB;CAC3B,MAAM;CACN,SAAS;CACT,WAAW;CACX,gBAAgB;AACnB;AACD,SAAS,aAAa,SAAS,OAAO;CAClC,IAAI,gBAAgB,MAAM,OAAO,UAAU,MAAM;AACjD,OAAM,SAAS,iBAAiB;AAChC,QAAO;AACV;AACD,SAAS,+BAA+B,IAAI,OAAO,OAAO;CACtD,IAAI,QAAQ,GAAG,MAAM;CACrB,IAAI,eAAe,CAAE;CACrB,IAAI;CACJ,IAAI;AACJ,MAAK,OAAO,SAAU,MAAM;EACxB,IAAI,WAAW,iBAAiB,MAAM,KAAK;AAC3C,WAAS,YAAY;AACrB,qBAAmB,MAAM,CAAE,GAAE,UAAU,KAAK;EAC5C,IAAI,WAAW,SAAS;EACxB,IAAI,WAAW,SAAS;EACxB,IAAI,YAAY,KAAK,SAAS;EAC9B,IAAI,MAAM,UAAU;AACpB,OAAK,IACD;AAEJ,qBAAmB,UAAU,MAAM;EACnC,IAAI,WAAW,SAAS;AACxB,OAAK,IAAI,WAAW,UAAU;GAC1B,IAAI,KAAK,SAAS;AAClB,gBAAa,WAAW,aAAa,YAAY,EAAE,GAAG,GAAI;AAC1D,gBAAa,SAAS,KAAK,GAAG,KAAK;EACtC;AACD,OAAK,IAAI,aAAa,UAAU;GAC5B,IAAI,MAAM,SAAS,WAAW;AAC9B,OAAI,IAAI,QAAQ,iBAAiB,IAAI,EACjC,mBAAkB;EAEzB;CACJ,EAAC;AACF,MAAK,gBACD;AAEJ,OAAM,IAAI;CACV,IAAI,gBAAgB,aAAa,cAAc,MAAM;AACrD,QAAO,gBAAgB,QAAQ,kBAAkB,cAAc;AAClE;AACD,SAAS,cAAc,QAAQ;AAC3B,QAAO,SAAS,OAAO,GACjB,WAAW,UACP,kBAAkB,WAAW,UAAU,MACvC,sBAAsB,OAAO,GAAG,SAAS,KAC7C;AACT;AACD,SAAgB,mBAAmB,IAAI,OAAO,OAAO,WAAW;CAC5D,IAAI,YAAY,GAAG;CACnB,IAAI,MAAM,UAAU;CACpB,IAAI,gBAAgB,CAAE;AACtB,KAAI,cAAcC,sBAAc;EAC5B,IAAI,eAAe,+BAA+B,IAAI,OAAO,MAAM;AACnE,MAAI,aACA,eAAc,KAAK,aAAa;YAE1B,IACN;CAEP,YACS,IACN;CAEJ,IAAI,iBAAiB,CAAE;AACvB,MAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK;EAC1B,IAAI,WAAW,UAAU;EACzB,IAAI,SAAS,CAAC,SAAS,YAAY,GAAG,MAAO,GAAI;EACjD,IAAI,SAAS,cAAc,SAAS,SAAS,CAAC,OAAO;EACrD,IAAI,QAAQ,SAAS,UAAU;AAC/B,MAAI,OACA,QAAO,KAAK,OAAO;MAGnB,QAAO,KAAK,SAAS;AAEzB,MAAI,MACA,QAAO,KAAK,QAAQ,MAAO,IAAI;AAEnC,MAAI,SAAS,SAAS,CAClB,QAAO,KAAK,WAAW;EAE3B,IAAI,MAAM,OAAO,KAAK,IAAI;AAC1B,iBAAe,OAAO,eAAe,QAAQ,CAAC,KAAK,CAAE,CAAC;AACtD,iBAAe,KAAK,GAAG,KAAK,SAAS;CACxC;CACD,SAAS,yBAAyB,eAAe;EAC7C,IAAIC,cAAY,cAAc;EAC9B,IAAIC,QAAMD,YAAU;EACpB,IAAI,eAAe,CAAE;EACrB,IAAI,WAAW,CAAE;EACjB,IAAI,WAAW,CAAE;EACjB,IAAI,kCAAkC;EACtC,SAAS,0BAA0BE,YAAU,QAAQ,eAAe;GAChE,IAAI,SAAS,WAAS,WAAW;GACjC,IAAI,UAAU,WAAS,YAAY;AACnC,QAAK,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;IACpC,IAAI,QAAQ,OAAO;AACnB,QAAI,MAAM,cAAc,EAAE;KACtB,IAAI,MAAM,MAAM;KAChB,IAAI,WAAW,MAAM;AACrB,uBAAkB,WAAW,cAAc,SAAS;AACpD,SAAI,SACA,MAAK,IAAIC,MAAI,GAAGA,MAAI,IAAI,QAAQA,OAAK;MACjC,IAAI,KAAK,IAAIA;MACb,IAAIC,YAAU,KAAK,MAAM,GAAG,OAAO,UAAU,IAAI,GAAG;MACpD,IAAI,WAAW,cAAc,GAAG,OAAO;MACvC,IAAI,WAAW,GAAG;AAClB,UAAI,SAAS,SAAS,IAAI,SAAS,SAAS,EAAE;AAC1C,cAAOA,aAAW,OAAOA,cAAY,CAAE;AACvC,cAAOA,WAAS,YAAY,GAAG;AAC/B,WAAI,SACA,QAAOA,WAAS,mCAAmC;MAE1D;KACJ;IAER;GACJ;EACJ;AACD,OAAK,IAAID,MAAI,GAAGA,MAAIF,OAAKE,OAAK;GAC1B,IAAID,aAAWF,YAAUG;GACzB,IAAI,aAAaD,WAAS;AAC1B,QAAK,WACD,EAAC,aAAa,0BAA0BA,YAAU,aAAa;YAE1D,eAAe,QACpB,2BAA0BA,YAAU,SAAS;EAEpD;AACD,OAAK,IAAI,WAAW,cAAc;GAC9B,IAAI,YAAY,CAAE;AAClB,iBAAc,WAAW,GAAG;AAC5B,UAAO,WAAW,aAAa,SAAS;GACxC,IAAI,MAAM,sBAAsB,UAAU;GAC1C,IAAI,iBAAiB,aAAa,SAAS;AAC3C,YAAS,WAAW,MAAM,EACtB,WAAW,IACd,IAAG,CAAE;AACN,sBAAmB,SAAS,UAAU,UAAU;AAChD,OAAI,eACA,UAAS,SAAS,mCAAmC;EAE5D;EAED,IAAI;EACJ,IAAI,kBAAkB;AACtB,OAAK,IAAI,WAAW,UAAU;AAC1B,YAAS,WAAW,SAAS,YAAY,CAAE;GAC3C,IAAI,WAAW;GACf,IAAI,iBAAiB,SAAS,SAAS;AACvC,OAAI,QACA,QAAO,IAAIG;GAEf,IAAI,QAAQ,KAAK,KAAK;AACtB,QAAK,OAAO;AACZ,YAAS,SAAS,IAAI,gBAAgB,IAAI,SAAS,UAAU,KAAK;GAClE,IAAI,SAAS,KAAK,KAAK;AACvB,QAAK,WAAW,UAAU,QAAQ;AAC9B,sBAAkB;AAClB;GACH;AACD,OAAI,eACA,UAAS,SAAS,mCAAmC;EAE5D;AAED,OAAK,gBACD,MAAK,IAAI,WAAW,SAChB,QAAO,SAAS,SAAS;AAGjC,OAAK,UACD,MAAK,IAAIF,MAAI,GAAGA,MAAIF,OAAKE,OAAK;GAC1B,IAAID,aAAWF,YAAUG;GACzB,IAAI,aAAaD,WAAS;AAC1B,OAAI,eAAe,QACf,2BAA0BA,YAAU,UAAU,SAAU,UAAU;AAAE,WAAO,kBAAkB;GAAY,EAAC;EAEjH;EAEL,IAAI,WAAW,KAAK,SAAS;EAC7B,IAAI,yBAAyB;EAC7B,IAAI;AACJ,OAAK,IAAIC,MAAI,GAAGA,MAAI,SAAS,QAAQA,OAAK;GACtC,IAAI,KAAK,SAASA,MAAI;GACtB,IAAI,KAAK,SAASA;AAClB,OAAI,SAAS,IAAI,wBAAwB,SAAS,IAAI,qBAAqB;AACvE,6BAAyB;AACzB;GACH;AACD,qBAAkB,SAAS,IAAI;EAClC;AACD,MAAI,0BAA0B,iBAAiB;AAC3C,QAAK,IAAI,WAAW,SAChB,KAAI,SAAS,SAAS,oBAClB,QAAO,SAAS,SAAS;AAGjC,SAAM,sBAAsB;EAC/B;AACD,MAAI,OAAO,UAAU,SAAUC,WAAS;AAAE,UAAO,KAAK,SAASA,WAAS,CAAC,SAAS;EAAI,EAAC,CAAC,QAAQ;GAC5F,IAAI,gBAAgB,aAAa,UAAU,MAAM;AACjD,UAAO,gBAAgB,MAAM,cAAc,KAAK;EACnD;CACJ;AACD,MAAK,IAAI,OAAO,gBAAgB;EAC5B,IAAI,eAAe,yBAAyB,eAAe,KAAK;AAChE,MAAI,aACA,eAAc,KAAK,aAAa;CAEvC;AACD,KAAI,cAAc,QAAQ;EACtB,IAAI,YAAY,MAAM,OAAO,UAAU,YAAY;AACnD,QAAM,SAAS,MAAM,aAAa,EAC9B,WAAW,cAAc,KAAK,IAAI,CACrC;AACD,QAAM,WAAW;CACpB;AACJ;;;;ACjRD,SAAgB,kBAAkB,IAAI,OAAO,OAAO;AAChD,MAAK,GAAG,OACJ,KAAI,GAAG,UAAU,EAAE;EACf,IAAI,QAAQ,EACR,kBAAkB,OACrB;AACD,oBAAkB,OAAO,OAAO,OAAO,KAAK;CAC/C,OACI;EACD,IAAI,gBAAgB,GAAG,OAAO,YAAY,GAAG,OAAO,SAAS,QACvD,GAAG,OAAO,SAAS,QACnB,CAAE;EACR,IAAI,OAAO,cAAc;AACzB,OAAK,MAAM;GACP,IAAI,aAAa,GAAG,SAAS,GAAG,MAAM;GACtC,IAAI,aAAa,GAAG,OAAO,UACpB,GAAG,OAAO,OAAO,SACjB,GAAG,OAAO,OAAO,MAAM;GAC9B,IAAI,WAAW,GAAG,cAAc,QAAQ,SAAS,IAAI,IAC9C,cAAc,aACf;AACN,OAAI,SACA,QAAO,UAAU,SAAS;EAEjC;EACD,IAAI,YAAY,cAAc;AAC9B,MAAI,WAAW;GACX,IAAI,UAAW,cAAc,iBAAiB,GAAG,YAC3C,GAAG,UAAU,KACb;AACN,eAAY,YAAY;EAC3B;EACD,IAAI,QAAQ,EACR,QAAQ,UACX;AACD,MAAI,KACA,OAAM,OAAO;AAEjB,MAAI,cAAc,OACd,OAAM,SAAS,cAAc;AAEjC,MAAI,UACA,OAAM,kBAAkB;AAE5B,oBAAkB,OAAO,OAAO,OAAO,KAAK;CAC/C;AAER;AACD,SAAS,kBAAkB,OAAO,OAAO,OAAO,WAAW;CACvD,IAAI,WAAW,KAAK,UAAU,MAAM;CACpC,IAAI,YAAY,MAAM,cAAc;AACpC,MAAK,WAAW;AACZ,cAAY,MAAM,OAAO,UAAU,YAAY;AAC/C,QAAM,cAAc,YAAY;AAChC,QAAM,SAAS,MAAM,aAAa,YAAY,WAAW,OAAO;CACnE;AACD,OAAM,WAAW,MAAM,WAAY,MAAM,WAAW,MAAM,YAAa;AAC1E;;;;AC5CD,IAAI,QAAQ,KAAK;AACjB,SAAS,YAAY,KAAK;AACtB,QAAO,OAAO,SAAS,IAAI,IAAI;AAClC;AACD,SAAS,aAAa,KAAK;AACvB,QAAO,OAAO,WAAW,IAAI,UAAU;AAC1C;AACD,SAAS,cAAc,OAAO,OAAO,IAAI,OAAO;AAC5C,iBAAgB,SAAU,KAAK,KAAK;EAChC,IAAI,eAAe,QAAQ,UAAU,QAAQ;AAC7C,MAAI,gBAAgB,WAAW,IAAI,CAC/B,aAAY,OAAO,OAAO,KAAK,MAAM;WAEhC,gBAAgB,UAAU,IAAI,CACnC,YAAW,IAAI,OAAO,KAAK,MAAM;MAGjC,OAAM,OAAO;AAEjB,MAAI,gBAAgB,MAAM,OAAO,QAAQ,OACrC,OAAM,oBAAoB;CAEjC,GAAE,OAAO,IAAI,MAAM;AACpB,WAAU,IAAI,OAAO,MAAM;AAC9B;AACD,SAAS,YAAY,OAAO,IAAI;CAC5B,IAAI,WAAW,kBAAkB,GAAG;AACpC,KAAI,UAAU;AACV,WAAS,KAAK,SAAU,KAAK,KAAK;AAC9B,UAAO,SAAS,MAAM,CAAC,mBAAmB,KAAK,aAAa,IAAI,MAAM;EACzE,EAAC;AACF,MAAI,GAAG,UAAU,CACb,OAAM,mBAAmB,YAAY;CAE5C;AACJ;AACD,SAAS,cAAc,GAAG;AACtB,QAAO,aAAa,EAAE,KAAK,EAAE,IACtB,aAAa,EAAE,GAAG,IAClB,aAAa,EAAE,GAAG,IAClB,aAAa,EAAE,KAAK,EAAE;AAChC;AACD,SAAS,YAAY,GAAG;AACpB,QAAO,aAAa,EAAE,GAAG,IAAI,aAAa,EAAE,GAAG;AAClD;AACD,SAAS,aAAa,OAAO,GAAG,UAAU;AACtC,KAAI,OAAO,YAAY,EAAE,IAAI,cAAc,EAAE,GAAG;EAC5C,IAAI,MAAM,WAAW,KAAK;AAC1B,QAAM,YAAY,cAAc,EAAE,GAC5B,eAAe,MAAM,EAAE,KAAK,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,KAAK,IAAI,GAAG,MAAM,MAAM,aAAa,EAAE;CACvG;AACJ;AACD,SAAS,iBAAiB,OAAO,OAAO,KAAK;CACzC,IAAI,SAAS,MAAM;CACnB,IAAI,SAAS,CAAE;AACf,MAAK,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,SAAO,KAAK,MAAM,OAAO,GAAG,KAAK,IAAI,GAAG,IAAI;AAC5C,SAAO,KAAK,MAAM,OAAO,GAAG,KAAK,IAAI,GAAG,IAAI;CAC/C;AACD,OAAM,SAAS,OAAO,KAAK,IAAI;AAClC;AACD,SAAS,kBAAkB,OAAO;AAC9B,SAAQ,MAAM;AACjB;AACD,SAAS,mBAAmB,MAAM;CAC9B,IAAI,iBAAiB,IAAI,MAAM,SAAU,MAAM;AAC3C,gBAAe,SAAS,WAAW,CAAC,MAAM,IAAK,IAAG;CACrD,EAAC;AACF,QAAO,SAAU,OAAO,OAAO,KAAK;AAChC,OAAK,IAAI,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;GAC5C,IAAI,OAAO,eAAe;GAC1B,IAAI,MAAM,MAAM,KAAK;AACrB,OAAI,OAAO,KACP,OAAM,KAAK,MAAM,MAAM,MAAM,IAAI,GAAG;EAE3C;CACJ;AACJ;AACD,IAAI,mBAAmB;CACnB,QAAQ,CAAC,mBAAmB;EAAC;EAAM;EAAM;CAAI,EAAC,AAAC;CAC/C,UAAU,CAAC,kBAAkB,iBAAkB;CAC/C,SAAS,CAAC,kBAAkB,iBAAkB;AACjD;AACD,SAAS,kBAAkB,IAAI;CAC3B,IAAI,YAAY,GAAG;AACnB,MAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,IAClC,KAAI,UAAU,GAAG,eAAe,QAC5B,QAAO;AAGf,QAAO;AACV;AACD,SAAgB,aAAa,IAAI,OAAO;CACpC,IAAI,QAAQ,GAAG;CACf,IAAI,QAAQ,GAAG;CACf,IAAI,gBAAgB,iBAAiB,GAAG;CACxC,IAAI,QAAQ,CAAE;CACd,IAAI,eAAe,MAAM;CACzB,IAAI,YAAY;CAChB,IAAI,gBAAgB,GAAG,MAAM;CAC7B,IAAI,YAAa,MAAM,YAAY,iBAAiB,GAAG,IAAK;AAC5D,KAAI,kBACI,MAAM,gBACL,cAAc,OAAO,cAAc,GAAG,MAAM,OAC5C,gBAAgB,kBAAkB,GAAG,OACrC,gBAAgB,IAAI;AACzB,cAAY,GAAG;EACf,IAAI,MAAM,KAAK,IAAI,IAAI,UAAU;AACjC,gBAAc,GAAG,OAAO,OAAO,IAAI;CACtC,OACI;EACD,IAAI,iBAAiB,GAAG,QAAQ,GAAG,cAAc;AACjD,OAAK,GAAG,KACJ,IAAG,iBAAiB;EAExB,IAAI,OAAO,GAAG;AACd,MAAI,eAAe;AACf,QAAK,WAAW;AAChB,MAAG,UAAU,MAAM,GAAG,MAAM;AAC5B,MAAG,aAAa;EACnB;EACD,IAAI,cAAc,KAAK,YAAY;EACnC,IAAI,QAAQ;EACZ,IAAI,iBAAiB,MAAM;AAC3B,MAAI,MAAM,qBAAqB,gBACvB,kBACD,kBAAkB,MAAM,wBAAwB;AACnD,QAAK,eACD,kBAAiB,MAAM,mBAAmB,IAAIE;AAElD,kBAAe,MAAM,UAAU;AAC/B,QAAK,YAAY,gBAAgB,cAAc;AAC/C,kBAAe,aAAa;AAC5B,SAAM,mBAAmB;AACzB,SAAM,yBAAyB;EAClC;AACD,QAAM,IAAI,eAAe,QAAQ;CACpC;AACD,cAAa,OAAO,GAAG,UAAU;AACjC,eAAc,OAAO,OAAO,IAAI,MAAM;AACtC,aAAY,OAAO,GAAG;AACtB,OAAM,aAAa,mBAAmB,IAAI,OAAO,MAAM;AACvD,OAAM,YAAY,kBAAkB,IAAI,OAAO,MAAM;AACrD,QAAO,YAAY,WAAW,GAAG,KAAK,IAAI,MAAM;AACnD;AACD,SAAgB,cAAc,IAAI,OAAO;CACrC,IAAI,QAAQ,GAAG;CACf,IAAI,QAAQ,MAAM;AAClB,KAAI,UAAU,SAAS,MAAM,EACzB;MAAI,YAAY,MAAM,CAClB,SAAQ,MAAM;WAET,aAAa,MAAM,CACxB,SAAQ,MAAM,WAAW;CAC5B;AAEL,MAAK,MACD;CAEJ,IAAI,IAAI,MAAM,KAAK;CACnB,IAAI,IAAI,MAAM,KAAK;CACnB,IAAI,KAAK,MAAM;CACf,IAAI,KAAK,MAAM;CACf,IAAI,QAAQ;EACR,MAAM;EACN,OAAO;EACP,QAAQ;CACX;AACD,KAAI,EACA,OAAM,IAAI;AAEd,KAAI,EACA,OAAM,IAAI;AAEd,cAAa,OAAO,GAAG,UAAU;AACjC,eAAc,OAAO,OAAO,IAAI,MAAM;AACtC,aAAY,OAAO,GAAG;AACtB,OAAM,aAAa,mBAAmB,IAAI,OAAO,MAAM;AACvD,QAAO,YAAY,SAAS,GAAG,KAAK,IAAI,MAAM;AACjD;AAED,SAAgB,cAAc,IAAI,OAAO;CACrC,IAAI,QAAQ,GAAG;CACf,IAAI,OAAO,MAAM;AACjB,SAAQ,SAAS,QAAQ;AACzB,MAAK,QAAQ,MAAM,MAAM,EAAE,IAAI,MAAM,MAAM,EAAE,CACzC;CAEJ,IAAI,OAAO,MAAM,QAAQ;CACzB,IAAI,IAAI,MAAM,KAAK;CACnB,IAAI,IAAI,YAAY,MAAM,KAAK,GAAG,cAAc,KAAK,EAAE,MAAM,aAAa;CAC1E,IAAI,YAAY,qBAAqB,MAAM,cACpC,MAAM;CACb,IAAI,QAAQ;EACR,qBAAqB;EACrB,eAAe;CAClB;AACD,KAAI,gBAAgB,MAAM,EAAE;EACxB,IAAI,mBAAmB;EACvB,IAAI,YAAY,MAAM;EACtB,IAAI,WAAW,cAAc,MAAM,SAAS;AAC5C,OAAK,WAAW,SAAS,CACrB;EAEJ,IAAI,aAAa,MAAM,cAAc;EACrC,IAAI,aAAa,MAAM;AACvB,sBAAoB,eAAe,WAAW,kBAAkB,aAAa;AAC7E,MAAI,aAAa,cAAc,SAC3B,qBAAoB,gBAAgB,YAAY;AAEpD,MAAI,cAAc,eAAe,SAC7B,qBAAoB,iBAAiB,aAAa;AAEtD,QAAM,QAAQ;CACjB,MAEG,OAAM,QAAQ,WAAW;AAE7B,KAAI,KAAK,MAAM,KAAK,CAChB,OAAM,eAAe;AAEzB,KAAI,EACA,OAAM,IAAI;AAEd,KAAI,EACA,OAAM,IAAI;AAEd,cAAa,OAAO,GAAG,UAAU;AACjC,eAAc,OAAO,OAAO,IAAI,MAAM;AACtC,aAAY,OAAO,GAAG;AACtB,OAAM,aAAa,mBAAmB,IAAI,OAAO,MAAM;AACvD,QAAO,YAAY,QAAQ,GAAG,KAAK,IAAI,eAAkB,KAAK;AACjE;AACD,SAAgBC,QAAM,IAAI,OAAO;AAC7B,KAAI,cAAcC,aACd,QAAO,aAAa,IAAI,MAAM;UAEzB,cAAcC,cACnB,QAAO,cAAc,IAAI,MAAM;UAE1B,cAAcC,cACnB,QAAO,cAAc,IAAI,MAAM;AAEtC;AACD,SAAS,UAAU,IAAI,OAAO,OAAO;CACjC,IAAI,QAAQ,GAAG;AACf,KAAI,UAAU,MAAM,EAAE;EAClB,IAAI,YAAY,aAAa,GAAG;EAChC,IAAI,cAAc,MAAM;EACxB,IAAI,WAAW,YAAY;AAC3B,OAAK,UAAU;GACX,IAAI,cAAc,GAAG,gBAAgB;GACrC,IAAI,SAAS,YAAY;GACzB,IAAI,SAAS,YAAY;AACzB,QAAK,WAAW,OACZ;GAEJ,IAAI,UAAU,MAAM,iBAAiB;GACrC,IAAI,UAAU,MAAM,iBAAiB;GACrC,IAAI,SAAS,MAAM;GACnB,IAAI,KAAK,eAAe,MAAM,YAAY,EAAE,UAAU,GAAG,SAAS,QAAQ,GAAG;GAC7E,IAAI,QAAQ,SAAS,IAAI;GACzB,IAAI,QAAQ,SAAS,IAAI;GACzB,IAAI,eAAe,QAAQ,MAAM;AACjC,cAAW,MAAM,OAAO,OAAO,MAAM;AACrC,SAAM,KAAK,YAAY,YAAY,UAAU,UAAU;IACnD,MAAM;IACN,KAAK;IACL,KAAK;IACL,SAAS;IACT,UAAU;GACb,GAAE,CACC,YAAY,gBAAgB,IAAI;IAC5B,MAAM,UAAU;IAChB,MAAM,UAAU;IAChB,gBAAgB;IAChB,eAAe;IACf,iBAAiB;GACpB,EAAC,AACL,EAAC;AACF,eAAY,aAAa;EAC5B;AACD,QAAM,SAAS,SAAS,SAAS;CACpC;AACJ;AACD,SAAgB,YAAY,OAAO,OAAO,QAAQ,OAAO;CACrD,IAAI,MAAM,MAAM;CAChB,IAAI;CACJ,IAAI,gBAAgB,EAChB,iBAAiB,IAAI,SACf,mBACA,oBACT;AACD,KAAI,iBAAiB,IAAI,EAAE;AACvB,gBAAc;AACd,gBAAc,KAAK,IAAI;AACvB,gBAAc,KAAK,IAAI;AACvB,gBAAc,KAAK,IAAI;AACvB,gBAAc,KAAK,IAAI;CAC1B,WACQ,iBAAiB,IAAI,EAAE;AAC5B,gBAAc;AACd,gBAAc,KAAK,UAAU,IAAI,GAAG,GAAI;AACxC,gBAAc,KAAK,UAAU,IAAI,GAAG,GAAI;AACxC,gBAAc,IAAI,UAAU,IAAI,GAAG,GAAI;CAC1C,OACI;AAEG,WAAS,yBAAyB;AAEtC;CACH;CACD,IAAI,SAAS,IAAI;CACjB,IAAI,aAAa,CAAE;AACnB,MAAK,IAAI,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,EAAE,GAAG;EAC/C,IAAI,SAAS,OAAO,OAAO,GAAG,OAAO,GAAG,MAAM;EAC9C,IAAI,YAAY,OAAO,GAAG;EAC1B,IAAI,KAAK,eAAe,UAAU,EAAE,QAAQ,GAAG,OAAO,UAAU,GAAG;EACnE,IAAI,aAAa,EACb,UAAU,OACb;AACD,aAAW,gBAAgB;AAC3B,MAAI,UAAU,EACV,YAAW,kBAAkB;AAEjC,aAAW,KAAK,YAAY,QAAQ,IAAI,IAAI,WAAW,CAAC;CAC3D;CACD,IAAI,gBAAgB,YAAY,aAAa,IAAI,eAAe,WAAW;CAC3E,IAAI,cAAc,cAAc,cAAc;CAC9C,IAAI,gBAAgB,MAAM;CAC1B,IAAI,aAAa,cAAc;AAC/B,MAAK,YAAY;AACb,eAAa,MAAM,OAAO,OAAO,MAAM;AACvC,gBAAc,eAAe;AAC7B,gBAAc,KAAK;AACnB,QAAM,KAAK,cAAc,YAAY,aAAa,YAAY,eAAe,WAAW;CAC3F;AACD,OAAM,UAAU,SAAS,WAAW;AACvC;AACD,SAAgB,WAAW,IAAI,OAAO,QAAQ,OAAO;CACjD,IAAI,MAAM,GAAG,MAAM;CACnB,IAAI,eAAe,GAAG,iBAAiB;CACvC,IAAI,eAAe,CAAE;CACrB,IAAI,SAAS,IAAI;CACjB,IAAI,WAAW,WAAW;CAC1B,IAAI,UAAU,WAAW;CACzB,IAAI,UAAU,WAAW;CACzB,IAAI;AACJ,KAAI,eAAe,IAAI,EAAE;EACrB,IAAI,eAAe,IAAI;EACvB,IAAI,gBAAgB,IAAI;EACxB,IAAI,gBAAgB;EACpB,IAAI,eAAe,IAAI;AACvB,MAAI,SAAS,aAAa,CACtB,YAAW;WAEN,YAAY,aAAa,CAC9B,YAAW,aAAa;WAEnB,aAAa,aAAa,CAC/B,YAAW,aAAa,WAAW;AAEvC,aAAW,UAAU,aAAa;GAC9B,IAAI,SAAS;AACb,UAAO,cAAc,OAAO;AAC5B,UAAO,eAAe,OAAO;EAChC,WACQ,gBAAgB,QAAQ,iBAAiB,MAAM;GACpD,IAAI,mBAAmB,SAAU,OAAO,KAAK;AACzC,QAAI,OAAO;KACP,IAAI,QAAQ,MAAM;KAClB,IAAI,QAAQ,gBAAgB,IAAI;KAChC,IAAI,SAAS,iBAAiB,IAAI;AAClC,SAAI,MAAM,QAAQ,WACd;UAAI,SAAS;AACT,gBAAS;AACT,gBAAS,aAAa;MACzB,WACQ,SAAS;AACd,eAAQ;AACR,iBAAU,aAAa;MAC1B;;AAEL,WAAM,MAAM,QAAQ;AACpB,WAAM,MAAM,SAAS;AACrB,SAAI,OAAO;AACP,YAAM,aAAa,SAAS,MAAM;AAClC,YAAM,aAAa,UAAU,OAAO;KACvC;IACJ;GACJ;GACD,IAAI,eAAe,oBAAoB,UAAU,MAAM,IAAI,SAAU,KAAK;AACtE,gBAAY,iBAAiB,cAAc,IAAI;AAC/C,qBAAiB,OAAO,IAAI;GAC/B,EAAC;AACF,OAAI,gBAAgB,aAAa,SAAS,aAAa,QAAQ;AAC3D,mBAAe,gBAAgB,aAAa;AAC5C,oBAAgB,iBAAiB,aAAa;GACjD;EACJ;AACD,UAAQ,YAAY,SAAS,OAAO;GAChC,MAAM;GACN,OAAO;GACP,QAAQ;EACX,EAAC;AACF,eAAa,QAAQ;AACrB,eAAa,SAAS;CACzB,WACQ,IAAI,YAAY;AACrB,UAAQ,MAAM,IAAI,WAAW;AAC7B,eAAa,QAAQ,IAAI;AACzB,eAAa,SAAS,IAAI;CAC7B;AACD,MAAK,MACD;CAEJ,IAAI;CACJ,IAAI;AACJ,KAAI,SACA,gBAAe,gBAAgB;UAE1B,SAAS;AACd,kBAAgB;AAChB,iBAAe,aAAa,QAAQ,aAAa;CACpD,WACQ,SAAS;AACd,iBAAe;AACf,kBAAgB,aAAa,SAAS,aAAa;CACtD,MAEG,cAAa,eAAe;AAEhC,KAAI,gBAAgB,SAAS,MAAM,aAAa,CAC5C,cAAa,QAAQ;AAEzB,KAAI,iBAAiB,SAAS,MAAM,cAAc,CAC9C,cAAa,SAAS;CAE1B,IAAI,mBAAmB,sBAAsB,IAAI;AACjD,sBAAqB,aAAa,mBAAmB;CACrD,IAAI,eAAe,YAAY,WAAW,IAAI,cAAc,CAAC,KAAM,EAAC;CACpE,IAAI,aAAa,cAAc,aAAa;CAC5C,IAAI,eAAe,MAAM;CACzB,IAAI,YAAY,aAAa;AAC7B,MAAK,WAAW;AACZ,cAAY,MAAM,OAAO,OAAO,MAAM;AACtC,eAAa,cAAc;AAC3B,eAAa,KAAK;AAClB,iBAAe,MAAM,KAAK,aAAa,YAAY,WAAW,WAAW,cAAc,CAAC,KAAM,EAAC;CAClG;AACD,OAAM,UAAU,SAAS,UAAU;AACtC;AACD,SAAgB,YAAY,UAAU,OAAO,OAAO;CAChD,IAAI,gBAAgB,MAAM,eAAe,OAAO,MAAM;CACtD,IAAI,aAAa,cAAc,SAAS;AACxC,MAAK,YAAY;AACb,eAAa,MAAM,OAAO,OAAO,MAAM;EACvC,IAAI,gBAAgB,EAChB,IAAI,WACP;AACD,gBAAc,SAAS,MAAM;AAC7B,OAAK,cAAc,YAAY,YAAY,YAAY,eAAe,CAAC,aAAa,UAAU,MAAM,AAAC,EAAC;CACzG;AACD,OAAM,eAAe,SAAS,WAAW;AAC5C;;;;AC/dD,SAAgB,eAAe,MAAM;AACjC,QAAO,SAAS,eAAe,KAAK;AACvC;AAID,SAAgB,aAAaC,cAAY,SAAS,eAAe;AAC7D,cAAW,aAAa,SAAS,cAAc;AAClD;AACD,SAAgB,YAAY,MAAM,OAAO;AACrC,MAAK,YAAY,MAAM;AAC1B;AACD,SAAgB,YAAY,MAAM,OAAO;AACrC,MAAK,YAAY,MAAM;AAC1B;AACD,SAAgB,WAAW,MAAM;AAC7B,QAAO,KAAK;AACf;AACD,SAAgB,YAAY,MAAM;AAC9B,QAAO,KAAK;AACf;AAID,SAAgB,eAAe,MAAM,MAAM;AACvC,MAAK,cAAc;AACtB;;;;ACvBD,IAAI,YAAY;AAChB,IAAI,QAAQ;AACZ,IAAI,YAAY,YAAY,IAAI,GAAG;AACnC,SAAS,QAAQ,GAAG;AAChB,QAAO;AACV;AACD,SAAS,MAAM,GAAG;AACd,QAAO;AACV;AACD,SAAS,kBAAkB,UAAU,UAAU,QAAQ;CACnD,IAAIC,QAAM,CAAE;AACZ,MAAK,IAAI,IAAI,UAAU,KAAK,QAAQ,EAAE,GAAG;EACrC,IAAI,MAAM,SAAS,GAAG;AACtB,MAAI,gBAAmB;AAEf,OAAIA,MAAI,QAAQ,KACZ,SAAQ,MAAM,mBAAmB,IAAI;AAG7C,SAAI,OAAO;EACd;CACJ;AACD,QAAOA;AACV;AACD,SAAS,UAAU,QAAQ,QAAQ;CAC/B,IAAI,YAAY,OAAO,QAAQ,OAAO;CACtC,IAAI,YAAY,OAAO,QAAQ,OAAO;AACtC,QAAO,aAAa;AACvB;AACD,SAAS,UAAU,OAAO;CACtB,IAAI;CACJ,IAAI,WAAW,MAAM;CACrB,IAAI,MAAM,MAAM;AAChB,KAAI,MAAM,IAAI,EAAE;EACZ,IAAI,MAAO,MAAM,MAAM,cAAc,IAAI;AACzC,cAAY,WAAW,MAAM;AAC7B,MAAI,QAAQ,SAAS,CACjB,MAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;GAClC,IAAI,KAAK,SAAS;AAClB,OAAI,MAAM,KACN,aAAgB,KAAK,UAAU,GAAG,CAAC;EAE1C;WAEI,MAAM,MAAM,KAAK,KAAK,SAAS,MAAM,KAAK,CAC/C,aAAgB,KAAK,eAAmB,MAAM,KAAK,CAAC;CAE3D,MAEG,OAAM,MAAM,eAAmB,MAAM,KAAK;AAE9C,QAAO,MAAM;AAChB;AACD,SAAS,UAAU,WAAW,QAAQ,QAAQ,UAAU,QAAQ;AAC5D,QAAO,YAAY,QAAQ,EAAE,UAAU;EACnC,IAAI,KAAK,OAAO;AAChB,MAAI,MAAM,KACN,cAAiB,WAAW,UAAU,GAAG,EAAE,OAAO;CAEzD;AACJ;AACD,SAAS,aAAa,WAAW,QAAQ,UAAU,QAAQ;AACvD,QAAO,YAAY,QAAQ,EAAE,UAAU;EACnC,IAAI,KAAK,OAAO;AAChB,MAAI,MAAM,KACN,KAAI,MAAM,GAAG,IAAI,EAAE;GACf,IAAI,WAAW,WAAe,GAAG,IAAI;AACrC,eAAgB,UAAU,GAAG,IAAI;EACpC,MAEG,aAAgB,WAAW,GAAG,IAAI;CAG7C;AACJ;AACD,SAAgB,YAAY,UAAU,OAAO;CACzC,IAAI;CACJ,IAAI,MAAM,MAAM;CAChB,IAAI,WAAW,YAAY,SAAS,SAAS,CAAE;CAC/C,IAAI,QAAQ,MAAM,SAAS,CAAE;AAC7B,KAAI,aAAa,MACb;AAEJ,MAAK,OAAO,OAAO;EACf,IAAI,MAAM,MAAM;EAChB,IAAI,MAAM,SAAS;AACnB,MAAI,QAAQ,IACR,KAAI,QAAQ,KACR,KAAI,aAAa,KAAK,GAAG;WAEpB,QAAQ,MACb,KAAI,gBAAgB,IAAI;WAGpB,QAAQ,QACR,KAAI,MAAM,UAAU;WAEf,IAAI,WAAW,EAAE,KAAK,MAC3B,KAAI,aAAa,KAAK,IAAI;WAErB,QAAQ,iBAAiB,QAAQ,QACtC,KAAI,eAAe,OAAO,KAAK,IAAI;WAE9B,IAAI,WAAW,EAAE,KAAK,UAC3B,KAAI,eAAe,eAAe,KAAK,IAAI;WAEtC,IAAI,WAAW,EAAE,KAAK,UAC3B,KAAI,eAAe,SAAS,KAAK,IAAI;MAGrC,KAAI,aAAa,KAAK,IAAI;CAIzC;AACD,MAAK,OAAO,SACR,OAAM,OAAO,OACT,KAAI,gBAAgB,IAAI;AAGnC;AACD,SAAS,eAAe,WAAW,OAAO,OAAO;CAC7C,IAAI,cAAc;CAClB,IAAI,cAAc;CAClB,IAAI,YAAY,MAAM,SAAS;CAC/B,IAAI,gBAAgB,MAAM;CAC1B,IAAI,cAAc,MAAM;CACxB,IAAI,YAAY,MAAM,SAAS;CAC/B,IAAI,gBAAgB,MAAM;CAC1B,IAAI,cAAc,MAAM;CACxB,IAAI;CACJ,IAAI;CACJ,IAAI;CACJ,IAAI;AACJ,QAAO,eAAe,aAAa,eAAe,UAC9C,KAAI,iBAAiB,KACjB,iBAAgB,MAAM,EAAE;UAEnB,eAAe,KACpB,eAAc,MAAM,EAAE;UAEjB,iBAAiB,KACtB,iBAAgB,MAAM,EAAE;UAEnB,eAAe,KACpB,eAAc,MAAM,EAAE;UAEjB,UAAU,eAAe,cAAc,EAAE;AAC9C,aAAW,eAAe,cAAc;AACxC,kBAAgB,MAAM,EAAE;AACxB,kBAAgB,MAAM,EAAE;CAC3B,WACQ,UAAU,aAAa,YAAY,EAAE;AAC1C,aAAW,aAAa,YAAY;AACpC,gBAAc,MAAM,EAAE;AACtB,gBAAc,MAAM,EAAE;CACzB,WACQ,UAAU,eAAe,YAAY,EAAE;AAC5C,aAAW,eAAe,YAAY;AACtC,eAAiB,WAAW,cAAc,KAAK,YAAgB,YAAY,IAAI,CAAC;AAChF,kBAAgB,MAAM,EAAE;AACxB,gBAAc,MAAM,EAAE;CACzB,WACQ,UAAU,aAAa,cAAc,EAAE;AAC5C,aAAW,aAAa,cAAc;AACtC,eAAiB,WAAW,YAAY,KAAK,cAAc,IAAI;AAC/D,gBAAc,MAAM,EAAE;AACtB,kBAAgB,MAAM,EAAE;CAC3B,OACI;AACD,MAAI,QAAQ,YAAY,CACpB,eAAc,kBAAkB,OAAO,aAAa,UAAU;AAElE,aAAW,YAAY,cAAc;AACrC,MAAI,QAAQ,SAAS,CACjB,cAAiB,WAAW,UAAU,cAAc,EAAE,cAAc,IAAI;OAEvE;AACD,eAAY,MAAM;AAClB,OAAI,UAAU,QAAQ,cAAc,IAChC,cAAiB,WAAW,UAAU,cAAc,EAAE,cAAc,IAAI;QAEvE;AACD,eAAW,WAAW,cAAc;AACpC,UAAM;AACN,iBAAiB,WAAW,UAAU,KAAK,cAAc,IAAI;GAChE;EACJ;AACD,kBAAgB,MAAM,EAAE;CAC3B;AAEL,KAAI,eAAe,aAAa,eAAe,UAC3C,KAAI,cAAc,WAAW;AACzB,WAAS,MAAM,YAAY,MAAM,OAAO,OAAO,MAAM,YAAY,GAAG;AACpE,YAAU,WAAW,QAAQ,OAAO,aAAa,UAAU;CAC9D,MAEG,cAAa,WAAW,OAAO,aAAa,UAAU;AAGjE;AACD,SAAS,WAAW,UAAU,OAAO;CACjC,IAAI,MAAO,MAAM,MAAM,SAAS;CAChC,IAAI,QAAQ,SAAS;CACrB,IAAI,KAAK,MAAM;AACf,KAAI,aAAa,MACb;AAEJ,aAAY,UAAU,MAAM;AAC5B,KAAI,QAAQ,MAAM,KAAK,EACnB;MAAI,MAAM,MAAM,IAAI,MAAM,GAAG,EACzB;OAAI,UAAU,GACV,gBAAe,KAAK,OAAO,GAAG;EACjC,WAEI,MAAM,GAAG,EAAE;AAChB,OAAI,MAAM,SAAS,KAAK,CACpB,gBAAmB,KAAK,GAAG;AAE/B,aAAU,KAAK,MAAM,IAAI,GAAG,GAAG,SAAS,EAAE;EAC7C,WACQ,MAAM,MAAM,CACjB,cAAa,KAAK,OAAO,GAAG,MAAM,SAAS,EAAE;WAExC,MAAM,SAAS,KAAK,CACzB,gBAAmB,KAAK,GAAG;CAC9B,WAEI,SAAS,SAAS,MAAM,MAAM;AACnC,MAAI,MAAM,MAAM,CACZ,cAAa,KAAK,OAAO,GAAG,MAAM,SAAS,EAAE;AAEjD,iBAAmB,KAAK,MAAM,KAAK;CACtC;AACJ;AACD,SAAwB,MAAM,UAAU,OAAO;AAC3C,KAAI,UAAU,UAAU,MAAM,CAC1B,YAAW,UAAU,MAAM;MAE1B;EACD,IAAI,MAAM,SAAS;EACnB,IAAI,WAAW,WAAe,IAAI;AAClC,YAAU,MAAM;AAChB,MAAI,aAAa,MAAM;AACnB,gBAAiB,UAAU,MAAM,KAAK,YAAgB,IAAI,CAAC;AAC3D,gBAAa,UAAU,CAAC,QAAS,GAAE,GAAG,EAAE;EAC3C;CACJ;AACD,QAAO;AACV;;;;ACtPD,IAAI,QAAQ;AACZ,IAAI,aAAc,WAAY;CAC1B,SAASC,aAAW,MAAM,SAAS,MAAM;AACrC,OAAK,OAAO;AACZ,OAAK,eAAe,uBAAuB,eAAe;AAC1D,OAAK,cAAc,uBAAuB,cAAc;AACxD,OAAK,UAAU;AACf,OAAK,QAAQ,OAAO,OAAO,CAAE,GAAE,KAAK;AACpC,OAAK,OAAO;AACZ,OAAK,MAAM,OAAO;AAClB,OAAK,YAAY,eAAe,KAAK,OAAO,KAAK,OAAO;AACxD,MAAI,SAAS,KAAK,KAAK;GACnB,IAAI,WAAW,KAAK,YAAY,SAAS,cAAc,MAAM;AAC7D,YAAS,MAAM,UAAU;GACzB,IAAI,SAAS,KAAK,UAAU,KAAK,UAAU,MAAM,cAAc,MAAM;AACrE,eAAY,MAAM,KAAK,UAAU;AACjC,YAAS,YAAY,OAAO;AAC5B,QAAK,YAAY,SAAS;EAC7B;AACD,OAAK,OAAO,KAAK,OAAO,KAAK,OAAO;CACvC;AACD,cAAW,UAAU,UAAU,WAAY;AACvC,SAAO,KAAK;CACf;AACD,cAAW,UAAU,kBAAkB,WAAY;AAC/C,SAAO,KAAK;CACf;AACD,cAAW,UAAU,wBAAwB,WAAY;EACrD,IAAI,eAAe,KAAK,iBAAiB;AACzC,MAAI,aACA,QAAO;GACH,YAAY,aAAa,cAAc;GACvC,WAAW,aAAa,aAAa;EACxC;CAER;AACD,cAAW,UAAU,YAAY,WAAY;AACzC,SAAO,KAAK;CACf;AACD,cAAW,UAAU,UAAU,WAAY;AACvC,MAAI,KAAK,MAAM;GACX,IAAI,QAAQ,KAAK,cAAc,EAC3B,YAAY,KACf,EAAC;AACF,SAAM,MAAM,QAAQ;AACpB,SAAM,KAAK,WAAW,MAAM;AAC5B,QAAK,YAAY;EACpB;CACJ;AACD,cAAW,UAAU,mBAAmB,SAAU,IAAI;AAClD,SAAO,QAAM,IAAI,iBAAiB,KAAK,IAAI,CAAC;CAC/C;AACD,cAAW,UAAU,gBAAgB,SAAU,MAAM;AACjD,SAAO,QAAQ,CAAE;EACjB,IAAI,OAAO,KAAK,QAAQ,eAAe,KAAK;EAC5C,IAAI,QAAQ,KAAK;EACjB,IAAI,SAAS,KAAK;EAClB,IAAI,QAAQ,iBAAiB,KAAK,IAAI;AACtC,QAAM,YAAY,KAAK;AACvB,QAAM,aAAa,KAAK;AACxB,QAAM,WAAW,KAAK;AACtB,QAAM,WAAW,KAAK;AACtB,QAAM,MAAM,KAAK,MAAM;EACvB,IAAI,WAAW,CAAE;EACjB,IAAI,UAAU,KAAK,WAAW,sBAAsB,OAAO,QAAQ,KAAK,kBAAkB,MAAM;AAChG,aAAW,SAAS,KAAK,QAAQ;EACjC,IAAI,aAAa,KAAK,WACf,KAAK,aAAa,YAAY,KAAK,QAAQ,CAAE,GAAE,CAAE,EAAC,GAAI;AAC7D,OAAK,WAAW,MAAM,OAAO,YAAY,UAAU,WAAW,SAAS;AACvE,eAAa,SAAS,KAAK,UAAU;EACrC,IAAI,OAAO,IAAI,KAAK,MAAM,KAAK,EAAE,SAAU,IAAI;AAAE,UAAO,MAAM,KAAK;EAAM,EAAC;AAC1E,MAAI,KAAK,OACL,UAAS,KAAK,YAAY,QAAQ,QAAQ,CAAE,GAAE,KAAK,CAAC;AAExD,MAAI,KAAK,WAAW;GAChB,IAAI,kBAAkB,aAAa,MAAM,UAAU,MAAM,UAAU,EAAE,SAAS,KAAM,EAAC;AACrF,OAAI,iBAAiB;IACjB,IAAI,YAAY,YAAY,SAAS,OAAO,CAAE,GAAE,CAAE,GAAE,gBAAgB;AACpE,aAAS,KAAK,UAAU;GAC3B;EACJ;AACD,SAAO,eAAe,OAAO,QAAQ,UAAU,KAAK,WAAW;CAClE;AACD,cAAW,UAAU,iBAAiB,SAAU,MAAM;AAClD,SAAO,QAAQ,CAAE;AACjB,SAAO,cAAc,KAAK,cAAc;GACpC,WAAW,UAAU,KAAK,cAAc,KAAK;GAC7C,UAAU,UAAU,KAAK,aAAa,KAAK;GAC3C,YAAY;GACZ,UAAU;GACV,YAAY,UAAU,KAAK,YAAY,KAAK;EAC/C,EAAC,EAAE,EAAE,SAAS,KAAM,EAAC;CACzB;AACD,cAAW,UAAU,qBAAqB,SAAU,iBAAiB;AACjE,OAAK,mBAAmB;CAC3B;AACD,cAAW,UAAU,aAAa,WAAY;AAC1C,SAAO,KAAK,cAAc,KAAK,WAAW;CAC7C;AACD,cAAW,UAAU,aAAa,SAAU,MAAM,OAAO,KAAK;EAC1D,IAAI,UAAU,KAAK;EACnB,IAAI,uBAAuB,CAAE;EAC7B,IAAI,4BAA4B;EAChC,IAAI;EACJ,IAAI;EACJ,IAAI,mBAAmB;AACvB,OAAK,IAAI,IAAI,GAAG,IAAI,SAAS,KAAK;GAC9B,IAAI,cAAc,KAAK;AACvB,QAAK,YAAY,WAAW;IACxB,IAAI,YAAY,YAAY;IAC5B,IAAI,MAAM,aAAa,UAAU,UAAU;IAC3C,IAAI,UAAU,iBAAiB,cAAc,UAAU;IACvD,IAAI,WAAW;AACf,SAAK,MAAM,KAAK,IAAI,MAAM,GAAG,UAAU,EAAE,EAAE,OAAO,GAAG,MACjD,KAAI,aAAa,iBACV,UAAU,SAAS,cAAc,KACpC;AAGR,SAAK,IAAI,MAAM,UAAU,GAAG,MAAM,KAAK,OAAO;AAC1C;AACA,4BAAuB,qBAAqB,4BAA4B;IAC3E;AACD,SAAK,IAAI,MAAM,MAAM,GAAG,MAAM,KAAK,OAAO;KACtC,IAAI,aAAa,CAAE;AACnB,iBAAY,UAAU,MAAM,YAAY,MAAM;KAC9C,IAAI,IAAI,YAAY,KAAK,YAAY,oBAAoB,YAAY,CAAE,EAAC;AACxE,MAAC,uBAAuB,qBAAqB,WAAW,KAAK,KAAK,EAAE;AACpE,0BAAqB,+BAA+B;AACpD,4BAAuB;IAC1B;AACD,oBAAgB;IAChB,IAAI,MAAM,QAAM,aAAa,MAAM;AACnC,QAAI,IACA,EAAC,uBAAuB,qBAAqB,WAAW,KAAK,KAAK,IAAI;GAE7E;EACJ;CACJ;AACD,cAAW,UAAU,SAAS,SAAU,OAAO,QAAQ;EACnD,IAAI,OAAO,KAAK;EAChB,IAAI,OAAO,KAAK;EAChB,IAAI,WAAW,KAAK;AACpB,WAAS,SAAS,KAAK,QAAQ;AAC/B,YAAU,SAAS,KAAK,SAAS;AACjC,MAAI,QAAQ,UAAU;AAClB,YAAS,MAAM,UAAU;AACzB,WAAQ,QAAQ,MAAM,GAAG,KAAK;AAC9B,YAAS,QAAQ,MAAM,GAAG,KAAK;AAC/B,YAAS,MAAM,UAAU;EAC5B;AACD,MAAI,KAAK,WAAW,SAAS,KAAK,YAAY,QAAQ;AAClD,QAAK,SAAS;AACd,QAAK,UAAU;AACf,OAAI,UAAU;IACV,IAAI,gBAAgB,SAAS;AAC7B,kBAAc,QAAQ,QAAQ;AAC9B,kBAAc,SAAS,SAAS;GACnC;AACD,QAAK,UAAU,KAAK,iBAAiB,EAAE;IACnC,IAAI,SAAS,KAAK;AAClB,QAAI,QAAQ;AACR,YAAO,aAAa,SAAS,MAAM;AACnC,YAAO,aAAa,UAAU,OAAO;IACxC;IACD,IAAI,OAAO,KAAK,YAAY,KAAK,SAAS;AAC1C,QAAI,MAAM;AACN,UAAK,aAAa,SAAS,MAAM;AACjC,UAAK,aAAa,UAAU,OAAO;IACtC;GACJ,MAEG,MAAK,SAAS;EAErB;CACJ;AACD,cAAW,UAAU,WAAW,WAAY;AACxC,SAAO,KAAK;CACf;AACD,cAAW,UAAU,YAAY,WAAY;AACzC,SAAO,KAAK;CACf;AACD,cAAW,UAAU,UAAU,WAAY;AACvC,MAAI,KAAK,KACL,MAAK,KAAK,YAAY;AAE1B,OAAK,UACD,KAAK,YACD,KAAK,UACD,KAAK,YACD,KAAK,WACD,KAAK,aAAa;CACzC;AACD,cAAW,UAAU,QAAQ,WAAY;AACrC,MAAI,KAAK,QACL,MAAK,QAAQ,YAAY;AAE7B,OAAK,YAAY;CACpB;AACD,cAAW,UAAU,YAAY,SAAU,QAAQ;EAC/C,IAAI,MAAM,KAAK,gBAAgB;EAC/B,IAAI,SAAS;AACb,MAAI,QAAQ;AACR,SAAM,aAAa,IAAI;AACvB,UAAO,OAAO,SAAS,YAAY;EACtC;AACD,SAAO,SAAS,mBAAmB,mBAAmB,IAAI;CAC7D;AACD,QAAOA;AACV,GAAE;AACH,SAAS,uBAAuB,QAAQ;AACpC,QAAO,WAAY;AAEX,WAAS,8CAA6C,SAAS,KAAI;CAE1E;AACJ;AACD,SAAS,sBAAsB,OAAO,QAAQ,iBAAiB,OAAO;CAClE,IAAI;AACJ,KAAI,mBAAmB,oBAAoB,QAAQ;AAC/C,YAAU,YAAY,QAAQ,MAAM;GACzB;GACC;GACR,GAAG;GACH,GAAG;EACN,EAAC;AACF,MAAI,WAAW,gBAAgB,CAC3B,aAAY,EAAE,MAAM,gBAAiB,GAAE,QAAQ,OAAO,QAAQ,MAAM;WAE/D,UAAU,gBAAgB,CAC/B,YAAW;GACP,OAAO,EACH,MAAM,gBACT;GACD,OAAO;GACP,iBAAiB,WAAY;AAAE,WAAQ;KAAS;KAAe;IAAQ;GAAI;EAC9E,GAAE,QAAQ,OAAO,QAAQ,MAAM;OAE/B;GACD,IAAI,KAAK,eAAe,gBAAgB,EAAE,QAAQ,GAAG,OAAO,UAAU,GAAG;AACzE,WAAQ,MAAM,OAAO;AACrB,aAAU,MAAM,QAAQ,MAAM,kBAAkB;EACnD;CACJ;AACD,QAAO;AACV;AACD,wBAAe;;;;AChNf,SAAgBC,UAAQ,WAAW;AACjC,WAAU,gBAAgB,OAAOC,kBAAW;AAC7C;;;;ACrCD,SAAS,UAAU,IAAI,SAAS,KAAK;CACjC,IAAI,SAAS,YAAY,cAAc;CACvC,IAAI,QAAQ,QAAQ,UAAU;CAC9B,IAAI,SAAS,QAAQ,WAAW;CAChC,IAAI,cAAc,OAAO;AACzB,KAAI,aAAa;AACb,cAAY,WAAW;AACvB,cAAY,OAAO;AACnB,cAAY,MAAM;AAClB,cAAY,QAAQ,QAAQ;AAC5B,cAAY,SAAS,SAAS;AAC9B,SAAO,aAAa,kBAAkB,GAAG;CAC5C;AACD,QAAO,QAAQ,QAAQ;AACvB,QAAO,SAAS,SAAS;AACzB,QAAO;AACV;AAED,IAAI,QAAS,SAAU,QAAQ;AAC3B,WAAUC,SAAO,OAAO;CACxB,SAASA,QAAM,IAAI,SAAS,KAAK;EAC7B,IAAI,QAAQ,OAAO,KAAK,KAAK,IAAI;AACjC,QAAM,aAAa;AACnB,QAAM,iBAAiB;AACvB,QAAM,MAAM;AACZ,QAAM,UAAU;AAChB,QAAM,SAAS,CAAE;AACjB,QAAM,cAAc;AACpB,QAAM,SAAS;AACf,QAAM,sBAAsB;AAC5B,QAAM,UAAU;AAChB,QAAM,mBAAmB;AACzB,QAAM,SAAS;AACf,QAAM,cAAc;AACpB,QAAM,eAAe;AACrB,QAAM,aAAa;AACnB,QAAM,mBAAmB;AACzB,QAAM,iBAAiB;EACvB,IAAI;AACJ,QAAM,OAAO;AACb,aAAW,OAAO,SACd,OAAM,UAAU,IAAI,SAAS,IAAI;WAE5B,SAAc,GAAG,EAAE;AACxB,SAAM;AACN,QAAK,IAAI;EACZ;AACD,QAAM,KAAK;AACX,QAAM,MAAM;EACZ,IAAI,WAAW,IAAI;AACnB,MAAI,UAAU;AACV,qBAAuB,IAAI;AAC3B,OAAI,gBAAgB,WAAY;AAAE,WAAO;GAAQ;AACjD,YAAS,UAAU;AACnB,YAAS,SAAS;AAClB,YAAS,cAAc;EAC1B;AACD,QAAM,UAAU;AAChB,QAAM,MAAM;AACZ,SAAO;CACV;AACD,SAAM,UAAU,kBAAkB,WAAY;AAC1C,SAAO,KAAK,aAAa,KAAK;CACjC;AACD,SAAM,UAAU,aAAa,WAAY;AACrC,OAAK,mBAAmB,KAAK;AAC7B,OAAK,iBAAiB,KAAK;CAC9B;AACD,SAAM,UAAU,cAAc,WAAY;AACtC,OAAK,MAAM,KAAK,IAAI,WAAW,KAAK;AACpC,OAAK,IAAI,MAAM,KAAK;CACvB;AACD,SAAM,UAAU,eAAe,WAAY;AACvC,OAAK,mBAAmB;CAC3B;AACD,SAAM,UAAU,mBAAmB,WAAY;EAC3C,IAAI,MAAM,KAAK;AACf,OAAK,UAAU,UAAU,UAAU,KAAK,IAAI,KAAK,SAAS,IAAI;AAC9D,OAAK,UAAU,KAAK,QAAQ,WAAW,KAAK;AAC5C,MAAI,QAAQ,EACR,MAAK,QAAQ,MAAM,KAAK,IAAI;CAEnC;AACD,SAAM,UAAU,qBAAqB,SAAU,aAAa,UAAU,WAAW,YAAY;AACzF,MAAI,KAAK,kBAAkB;AACvB,QAAK,mBAAmB;AACxB,UAAO;EACV;EACD,IAAI,qBAAqB,CAAE;EAC3B,IAAI,sBAAsB,KAAK;EAC/B,IAAI,OAAO;EACX,IAAI,cAAc,IAAIC,qBAAa,GAAG,GAAG,GAAG;EAC5C,SAAS,mBAAmB,MAAM;AAC9B,QAAK,KAAK,UAAU,IAAI,KAAK,QAAQ,CACjC;AAEJ,OAAI,mBAAmB,WAAW,GAAG;IACjC,IAAI,eAAe,IAAIA,qBAAa,GAAG,GAAG,GAAG;AAC7C,iBAAa,KAAK,KAAK;AACvB,uBAAmB,KAAK,aAAa;GACxC,OACI;IACD,IAAI,WAAW;IACf,IAAI,eAAe;IACnB,IAAI,qBAAqB;AACzB,SAAK,IAAIC,MAAI,GAAGA,MAAI,mBAAmB,QAAQ,EAAEA,KAAG;KAChD,IAAI,aAAa,mBAAmBA;AACpC,SAAI,WAAW,UAAU,KAAK,EAAE;MAC5B,IAAI,gBAAgB,IAAID,qBAAa,GAAG,GAAG,GAAG;AAC9C,oBAAc,KAAK,WAAW;AAC9B,oBAAc,MAAM,KAAK;AACzB,yBAAmBC,OAAK;AACxB,iBAAW;AACX;KACH,WACQ,MAAM;AACX,kBAAY,KAAK,KAAK;AACtB,kBAAY,MAAM,WAAW;MAC7B,IAAI,QAAQ,KAAK,QAAQ,KAAK;MAC9B,IAAI,QAAQ,WAAW,QAAQ,WAAW;MAC1C,IAAI,cAAc,YAAY,QAAQ,YAAY;MAClD,IAAI,YAAY,cAAc,QAAQ;AACtC,UAAI,YAAY,cAAc;AAC1B,sBAAe;AACf,4BAAqBA;MACxB;KACJ;IACJ;AACD,QAAI,MAAM;AACN,wBAAmB,oBAAoB,MAAM,KAAK;AAClD,gBAAW;IACd;AACD,SAAK,UAAU;KACX,IAAI,eAAe,IAAID,qBAAa,GAAG,GAAG,GAAG;AAC7C,kBAAa,KAAK,KAAK;AACvB,wBAAmB,KAAK,aAAa;IACxC;AACD,SAAK,KACD,QAAO,mBAAmB,UAAU;GAE3C;EACJ;AACD,OAAK,IAAI,IAAI,KAAK,cAAc,IAAI,KAAK,YAAY,EAAE,GAAG;GACtD,IAAI,KAAK,YAAY;AACrB,OAAI,IAAI;IACJ,IAAI,cAAc,GAAG,gBAAgB,WAAW,YAAY,MAAM,KAAK;IACvE,IAAI,WAAW,GAAG,iBAAkB,GAAG,UAAU,eAAgB,eAC3D,GAAG,kBAAkB,GACrB;AACN,QAAI,SACA,oBAAmB,SAAS;IAEhC,IAAI,UAAU,gBAAiB,GAAG,UAAU,eAAgB,GAAG,gBACzD,GAAG,cAAc,GACjB;AACN,QAAI,QACA,oBAAmB,QAAQ;GAElC;EACJ;AACD,OAAK,IAAI,IAAI,KAAK,kBAAkB,IAAI,KAAK,gBAAgB,EAAE,GAAG;GAC9D,IAAI,KAAK,SAAS;GAClB,IAAI,cAAc,MAAM,GAAG,gBAAgB,WAAW,YAAY,MAAM,KAAK;AAC7E,OAAI,QAAQ,gBAAgB,GAAG,SAAS,GAAG,cAAc;IACrD,IAAI,WAAW,GAAG,kBAAkB;AACpC,QAAI,SACA,oBAAmB,SAAS;GAEnC;EACJ;EACD,IAAI;AACJ,KAAG;AACC,sBAAmB;AACnB,QAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,SAAS;AAC5C,QAAI,mBAAmB,GAAG,QAAQ,EAAE;AAChC,wBAAmB,OAAO,GAAG,EAAE;AAC/B;IACH;AACD,SAAK,IAAI,IAAI,IAAI,GAAG,IAAI,mBAAmB,QACvC,KAAI,mBAAmB,GAAG,UAAU,mBAAmB,GAAG,EAAE;AACxD,wBAAmB;AACnB,wBAAmB,GAAG,MAAM,mBAAmB,GAAG;AAClD,wBAAmB,OAAO,GAAG,EAAE;IAClC,MAEG;AAGR;GACH;EACJ,SAAQ;AACT,OAAK,cAAc;AACnB,SAAO;CACV;AACD,SAAM,UAAU,qBAAqB,WAAY;AAC7C,SAAO,CAAC,KAAK,eAAe,CAAE,GAAE,OAAO;CAC1C;AACD,SAAM,UAAU,SAAS,SAAU,OAAO,QAAQ;EAC9C,IAAI,MAAM,KAAK;EACf,IAAI,MAAM,KAAK;EACf,IAAI,WAAW,IAAI;EACnB,IAAI,UAAU,KAAK;AACnB,MAAI,UAAU;AACV,YAAS,QAAQ,QAAQ;AACzB,YAAS,SAAS,SAAS;EAC9B;AACD,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,SAAS;AACtB,MAAI,SAAS;AACT,WAAQ,QAAQ,QAAQ;AACxB,WAAQ,SAAS,SAAS;AAC1B,OAAI,QAAQ,EACR,MAAK,QAAQ,MAAM,KAAK,IAAI;EAEnC;CACJ;AACD,SAAM,UAAU,QAAQ,SAAU,UAAU,YAAY,cAAc;EAClE,IAAI,MAAM,KAAK;EACf,IAAI,MAAM,KAAK;EACf,IAAI,QAAQ,IAAI;EAChB,IAAI,SAAS,IAAI;AACjB,eAAa,cAAc,KAAK;EAChC,IAAI,iBAAiB,KAAK,eAAe;EACzC,IAAI,iBAAiB,KAAK;EAC1B,IAAI,MAAM,KAAK;EACf,IAAI,OAAO;AACX,MAAI,gBAAgB;AAChB,QAAK,KAAK,QACN,MAAK,kBAAkB;AAE3B,QAAK,QAAQ,2BAA2B;AACxC,QAAK,QAAQ,UAAU,KAAK,GAAG,GAAG,QAAQ,KAAK,SAAS,IAAI;EAC/D;EACD,IAAI,UAAU,KAAK;EACnB,SAAS,QAAQ,GAAG,GAAGE,SAAOC,UAAQ;AAClC,OAAI,UAAU,GAAG,GAAGD,SAAOC,SAAO;AAClC,OAAI,cAAc,eAAe,eAAe;IAC5C,IAAI,mCAAmC;AACvC,QAAI,iBAAsB,WAAW,EAAE;KACnC,IAAI,cAAc,WAAW,UAAW,WAAW,YAAYD,WACxD,WAAW,aAAaC;AAC/B,mCAA8B,eACvB,WAAW,oBACX,kBAAkB,KAAK,YAAY;MAClC,GAAG;MACH,GAAG;MACH,OAAOD;MACP,QAAQC;KACX,EAAC;AACN,gBAAW,mBAAmB;AAC9B,gBAAW,UAAUD;AACrB,gBAAW,WAAWC;IACzB,WACQ,qBAA0B,WAAW,EAAE;AAC5C,gBAAW,SAAS,WAAW,UAAU;AACzC,gBAAW,SAAS,WAAW,UAAU;AACzC,mCAA8B,oBAAoB,KAAK,YAAY,EAC/D,OAAO,WAAY;AACf,WAAK,cAAc;AACnB,WAAK,QAAQ,SAAS;KACzB,EACJ,EAAC;IACL;AACD,QAAI,MAAM;AACV,QAAI,YAAY,+BAA+B;AAC/C,QAAI,SAAS,GAAG,GAAGD,SAAOC,SAAO;AACjC,QAAI,SAAS;GAChB;AACD,OAAI,gBAAgB;AAChB,QAAI,MAAM;AACV,QAAI,cAAc;AAClB,QAAI,UAAU,SAAS,GAAG,GAAGD,SAAOC,SAAO;AAC3C,QAAI,SAAS;GAChB;EACJ;AAED,OAAK,gBAAgB,eACjB,SAAQ,GAAG,GAAG,OAAO,OAAO;WAEvB,aAAa,OAClB,MAAU,cAAc,SAAU,MAAM;AACpC,WAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,SAAS,IAAI;EAC3E,EAAC;CAET;AACD,QAAOJ;AACV,EAACK,iBAAS;AACX,oBAAe;;;;AChSf,IAAI,qBAAqB;AACzB,IAAI,gBAAgB;AACpB,IAAI,2BAA2B;AAC/B,IAAI,kBAAkB;AACtB,SAAS,aAAa,OAAO;AACzB,MAAK,MACD,QAAO;AAEX,KAAI,MAAM,YACN,QAAO;AAEX,YAAY,MAAM,WAAY,qBACf,MAAM,YAAa,WAC9B,QAAO;AAEX,QAAO;AACV;AACD,SAAS,WAAW,OAAO,QAAQ;CAC/B,IAAI,UAAU,SAAS,cAAc,MAAM;AAC3C,SAAQ,MAAM,UAAU;EACpB;EACA,WAAW,QAAQ;EACnB,YAAY,SAAS;EACrB;EACA;EACA;CACH,EAAC,KAAK,IAAI,GAAG;AACd,QAAO;AACV;AACD,IAAI,gBAAiB,WAAY;CAC7B,SAASC,gBAAc,MAAM,SAAS,MAAM,IAAI;AAC5C,OAAK,OAAO;AACZ,OAAK,cAAc,CAAE;AACrB,OAAK,mBAAmB,CAAE;AAC1B,OAAK,UAAU,CAAE;AACjB,OAAK,eAAe,CAAE;AACtB,OAAK,4BAA4B;AACjC,OAAK,OAAO;EACZ,IAAI,gBAAgB,KAAK,YAClB,KAAK,SAAS,aAAa,KAAK;AACvC,OAAK,QAAQ,OAAO,OAAY,CAAE,GAAE,QAAQ,CAAE,EAAC;AAC/C,OAAK,MAAM,KAAK,oBAAoB;AACpC,OAAK,gBAAgB;AACrB,OAAK,OAAO;EACZ,IAAI,YAAY,KAAK;AACrB,MAAI,WAAW;AACX,qBAAuB,KAAK;AAC5B,QAAK,YAAY;EACpB;AACD,OAAK,UAAU;EACf,IAAI,aAAa,KAAK;AACtB,OAAK,mBAAmB,CAAE;EAC1B,IAAI,SAAS,KAAK;AAClB,OAAK,cAAc;AACf,QAAK,SAAS,QAAQ,MAAM,GAAG,KAAK;AACpC,QAAK,UAAU,QAAQ,MAAM,GAAG,KAAK;GACrC,IAAI,UAAU,KAAK,WAAW,WAAW,KAAK,QAAQ,KAAK,QAAQ;AACnE,QAAK,YAAY,QAAQ;EAC5B,OACI;GACD,IAAI,aAAa;GACjB,IAAI,QAAQ,WAAW;GACvB,IAAI,SAAS,WAAW;AACxB,OAAI,KAAK,SAAS,KACd,SAAQ,KAAK;AAEjB,OAAI,KAAK,UAAU,KACf,UAAS,KAAK;AAElB,QAAK,MAAM,KAAK,oBAAoB;AACpC,cAAW,QAAQ,QAAQ,KAAK;AAChC,cAAW,SAAS,SAAS,KAAK;AAClC,QAAK,SAAS;AACd,QAAK,UAAU;GACf,IAAI,YAAY,IAAIC,cAAM,YAAY,MAAM,KAAK;AACjD,aAAU,cAAc;AACxB,aAAU,aAAa;AACvB,UAAO,iBAAiB;AACxB,aAAU,SAAS;AACnB,cAAW,KAAK,cAAc;AAC9B,QAAK,WAAW;EACnB;CACJ;AACD,iBAAc,UAAU,UAAU,WAAY;AAC1C,SAAO;CACV;AACD,iBAAc,UAAU,iBAAiB,WAAY;AACjD,SAAO,KAAK;CACf;AACD,iBAAc,UAAU,kBAAkB,WAAY;AAClD,SAAO,KAAK;CACf;AACD,iBAAc,UAAU,wBAAwB,WAAY;EACxD,IAAI,eAAe,KAAK,iBAAiB;AACzC,MAAI,aACA,QAAO;GACH,YAAY,aAAa,cAAc;GACvC,WAAW,aAAa,aAAa;EACxC;CAER;AACD,iBAAc,UAAU,UAAU,SAAU,UAAU;EAClD,IAAI,OAAO,KAAK,QAAQ,eAAe,KAAK;EAC5C,IAAI,WAAW,KAAK;EACpB,IAAI,aAAa,KAAK;AACtB,OAAK,YAAY,KAAK,QAAQ;AAC9B,OAAK,WAAW,MAAM,UAAU,UAAU,KAAK,UAAU;AACzD,OAAK,IAAI,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;GACxC,IAAI,IAAI,WAAW;GACnB,IAAI,QAAQ,KAAK,QAAQ;AACzB,QAAK,MAAM,eAAe,MAAM,SAAS;IACrC,IAAI,aAAa,MAAM,IAAI,KAAK,mBAAmB;AACnD,UAAM,QAAQ,WAAW;GAC5B;EACJ;AACD,MAAI,KAAK,MAAM,aACX,MAAK,mBAAmB,KAAK,OAAO;AAExC,SAAO;CACV;AACD,iBAAc,UAAU,eAAe,WAAY;AAC/C,OAAK,gBAAgB,KAAK,QAAQ,eAAe,MAAM,CAAC;CAC3D;AACD,iBAAc,UAAU,kBAAkB,SAAU,MAAM;EACtD,IAAI,MAAM,KAAK;EACf,IAAI,aAAa,KAAK;AACtB,gBAAc,WAAW,OAAO;AAChC,OAAK,IACD;EAEJ,IAAI,QAAQ;GACR,SAAS;GACT,WAAW,KAAK;GAChB,YAAY,KAAK;EACpB;EACD,IAAI;AACJ,OAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK;GAC1B,IAAI,KAAK,KAAK;AACd,OAAI,GAAG,WAAW;AACd,SAAK,WACD,cAAa,KAAK,cAAc,KAAK,SAAS,mBAAmB;AAErE,SAAK,KAAK;AACN,WAAM,WAAW;AACjB,SAAI,MAAM;IACb;AACD,UAAM,KAAK,IAAI,OAAO,MAAM,MAAM,EAAE;GACvC;EACJ;AACD,MAAI,IACA,KAAI,SAAS;CAEpB;AACD,iBAAc,UAAU,gBAAgB,WAAY;AAChD,SAAO,KAAK,SAAS,mBAAmB;CAC3C;AACD,iBAAc,UAAU,WAAW,SAAU,KAAK,IAAI;AAClD,cAAY,KAAK,GAAG;CACvB;AACD,iBAAc,UAAU,aAAa,SAAU,MAAM,UAAU,UAAU,UAAU;AAC/E,MAAI,KAAK,cAAc,SACnB;AAEJ,aAAW,YAAY;AACvB,OAAK,mBAAmB,KAAK;EAC7B,IAAI,KAAK,KAAK,aAAa,MAAM,UAAU,SAAS,EAAE,WAAW,GAAG,UAAU,oBAAoB,GAAG;AACrG,MAAI,KAAK,0BACL,MAAK,oBAAoB;AAE7B,MAAI,kBACA,MAAK,gBAAgB,KAAK;AAE9B,OAAK,UAAU;GACX,IAAI,SAAS;AACb,iCAAsB,WAAY;AAC9B,WAAO,WAAW,MAAM,UAAU,UAAU,SAAS;GACxD,EAAC;EACL,MAEG,MAAK,UAAU,SAAU,OAAO;AAC5B,SAAM,cAAc,MAAM,YAAY;EACzC,EAAC;CAET;AACD,iBAAc,UAAU,qBAAqB,WAAY;EACrD,IAAI,MAAM,KAAK,SAAS,cAAc,CAAC;EACvC,IAAI,QAAQ,KAAK,SAAS;EAC1B,IAAI,SAAS,KAAK,SAAS;AAC3B,MAAI,UAAU,GAAG,GAAG,OAAO,OAAO;AAClC,OAAK,iBAAiB,SAAU,OAAO;AACnC,OAAI,MAAM,QACN,KAAI,UAAU,MAAM,KAAK,GAAG,GAAG,OAAO,OAAO;EAEpD,EAAC;CACL;AACD,iBAAc,UAAU,eAAe,SAAU,MAAM,UAAU,UAAU;EACvE,IAAI,QAAQ;EACZ,IAAI,YAAY,CAAE;EAClB,IAAI,eAAe,KAAK,MAAM;AAC9B,OAAK,IAAI,KAAK,GAAG,KAAK,KAAK,YAAY,QAAQ,MAAM;GACjD,IAAI,SAAS,KAAK,YAAY;GAC9B,IAAI,QAAQ,KAAK,QAAQ;AACzB,OAAI,MAAM,eACH,UAAU,KAAK,gBACd,MAAM,WAAW,UACrB,WAAU,KAAK,MAAM;EAE5B;EACD,IAAI,WAAW;EACf,IAAI,oBAAoB;EACxB,IAAI,UAAU,SAAUC,KAAG;GACvB,IAAIC,UAAQ,UAAUD;GACtB,IAAI,MAAMC,QAAM;GAChB,IAAI,eAAe,gBACZ,QAAM,mBAAmB,MAAM,UAAU,OAAO,QAAQ,OAAO,QAAQ;GAC9E,IAAI,QAAQ,WAAWA,QAAM,eAAeA,QAAM;GAClD,IAAI,YAAY,YAAYA,QAAM,eAAe,KAAK;GACtD,IAAI,YAAY,YAAY,KAAK,KAAK;GACtC,IAAI,aAAaA,QAAM,WAAW,OAAO,YAAY,KAC/C,OAAO,mBAAmB;AAChC,OAAIA,QAAM,iBAAiBA,QAAM,WAC7B,SAAM,MAAM,OAAO,YAAY,aAAa;YAEvC,UAAUA,QAAM,cAAc;IACnC,IAAI,UAAU,KAAK;AACnB,SAAK,QAAQ,gBAAgB,QAAQ,YAAY,SAC7C,SAAM,MAAM,OAAO,YAAY,aAAa;GAEnD;AACD,OAAI,UAAU,IAAI;AACd,YAAQ,MAAM,2CAA2C;AACzD,YAAQA,QAAM;GACjB;GACD,IAAI;GACJ,IAAI,UAAU,SAAU,aAAa;IACjC,IAAI,QAAQ;KACR,SAAS;KACT,YAAY;KACZ,QAAQ;KACR,WAAW,MAAM;KACjB,YAAY,MAAM;IACrB;AACD,SAAK,IAAI,OAAO,IAAIA,QAAM,YAAY,KAAK;KACvC,IAAI,KAAK,KAAK;AACd,SAAI,GAAG,UACH,qBAAoB;AAExB,WAAM,WAAW,IAAIA,SAAO,cAAc,aAAa,OAAO,MAAMA,QAAM,aAAa,EAAE;AACzF,SAAI,UAAU;MACV,IAAI,QAAQ,KAAK,KAAK,GAAG;AACzB,UAAI,QAAQ,GACR;KAEP;IACJ;AACD,QAAI,MAAM,gBACN,KAAI,SAAS;GAEpB;AACD,OAAI,aACA,KAAI,aAAa,WAAW,EACxB,KAAIA,QAAM;QAET;IACD,IAAI,MAAM,OAAO;AACjB,SAAK,IAAI,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;KAC1C,IAAI,OAAO,aAAa;AACxB,SAAI,MAAM;AACV,SAAI,WAAW;AACf,SAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,SAAS,IAAI;AACzE,SAAI,MAAM;AACV,aAAQ,KAAK;AACb,SAAI,SAAS;IAChB;GACJ;QAEA;AACD,QAAI,MAAM;AACV,aAAS;AACT,QAAI,SAAS;GAChB;AACD,WAAM,cAAc;AACpB,OAAIA,QAAM,cAAcA,QAAM,WAC1B,YAAW;EAElB;EACD,IAAI,SAAS;AACb,OAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,IAClC,SAAQ,EAAE;AAEd,MAAIC,YAAI,IACJ,MAAU,KAAK,SAAS,SAAUD,SAAO;AACrC,OAAIA,WAASA,QAAM,OAAOA,QAAM,IAAI,KAChC,SAAM,IAAI,MAAM;EAEvB,EAAC;AAEN,SAAO;GACO;GACS;EACtB;CACJ;AACD,iBAAc,UAAU,aAAa,SAAU,IAAI,cAAc,cAAc,aAAa,OAAO,QAAQ;EACvG,IAAI,MAAM,aAAa;AACvB,MAAI,cAAc;GACd,IAAI,YAAY,GAAG,cAAc;AACjC,QAAK,eAAe,aAAa,UAAU,UAAU,YAAY,EAAE;AAC/D,UAAM,KAAK,IAAI,OAAO,OAAO;AAC7B,OAAG,iBAAiB,UAAU;GACjC;EACJ,MAEG,OAAM,KAAK,IAAI,OAAO,OAAO;CAEpC;AACD,iBAAc,UAAU,WAAW,SAAU,QAAQ,SAAS;AAC1D,MAAI,KAAK,kBAAkB,KAAK,0BAC5B,UAAS;EAEb,IAAI,QAAQ,KAAK,QAAQ;AACzB,OAAK,OAAO;AACR,WAAQ,IAAIF,cAAM,QAAQ,QAAQ,MAAM,KAAK;AAC7C,SAAM,SAAS;AACf,SAAM,cAAc;AACpB,OAAI,KAAK,aAAa,QAClB,OAAW,OAAO,KAAK,aAAa,SAAS,KAAK;YAE7C,KAAK,aAAa,SAAS,0BAChC,OAAW,OAAO,KAAK,aAAa,SAAS,2BAA2B,KAAK;AAEjF,OAAI,QACA,OAAM,UAAU;AAEpB,QAAK,YAAY,QAAQ,MAAM;AAC/B,SAAM,aAAa;EACtB;AACD,SAAO;CACV;AACD,iBAAc,UAAU,cAAc,SAAU,QAAQ,OAAO;EAC3D,IAAI,YAAY,KAAK;EACrB,IAAI,aAAa,KAAK;EACtB,IAAI,MAAM,WAAW;EACrB,IAAI,UAAU,KAAK;EACnB,IAAI,YAAY;EAChB,IAAI,IAAI;AACR,MAAI,UAAU,SAAS;AAEf,YAAc,YAAY,SAAS,yBAAyB;AAEhE;EACH;AACD,OAAK,aAAa,MAAM,EAAE;AAElB,YAAc,qBAAqB,SAAS,gBAAgB;AAEhE;EACH;AACD,MAAI,MAAM,KAAK,SAAS,WAAW,IAAI;AACnC,QAAK,IAAI,GAAG,IAAI,MAAM,GAAG,IACrB,KAAI,WAAW,KAAK,UACb,WAAW,IAAI,KAAK,OACvB;AAGR,eAAY,UAAU,WAAW;EACpC;AACD,aAAW,OAAO,IAAI,GAAG,GAAG,OAAO;AACnC,YAAU,UAAU;AACpB,OAAK,MAAM,QACP,KAAI,WAAW;GACX,IAAI,UAAU,UAAU;AACxB,OAAI,QAAQ,YACR,SAAQ,aAAa,MAAM,KAAK,QAAQ,YAAY;OAGpD,SAAQ,YAAY,MAAM,IAAI;EAErC,WAEO,QAAQ,WACR,SAAQ,aAAa,MAAM,KAAK,QAAQ,WAAW;MAGnD,SAAQ,YAAY,MAAM,IAAI;AAI1C,QAAM,YAAY,MAAM,UAAU;CACrC;AACD,iBAAc,UAAU,YAAY,SAAU,IAAI,SAAS;EACvD,IAAI,aAAa,KAAK;AACtB,OAAK,IAAI,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;GACxC,IAAI,IAAI,WAAW;AACnB,MAAG,KAAK,SAAS,KAAK,QAAQ,IAAI,EAAE;EACvC;CACJ;AACD,iBAAc,UAAU,mBAAmB,SAAU,IAAI,SAAS;EAC9D,IAAI,aAAa,KAAK;AACtB,OAAK,IAAI,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;GACxC,IAAI,IAAI,WAAW;GACnB,IAAI,QAAQ,KAAK,QAAQ;AACzB,OAAI,MAAM,YACN,IAAG,KAAK,SAAS,OAAO,EAAE;EAEjC;CACJ;AACD,iBAAc,UAAU,iBAAiB,SAAU,IAAI,SAAS;EAC5D,IAAI,aAAa,KAAK;AACtB,OAAK,IAAI,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;GACxC,IAAI,IAAI,WAAW;GACnB,IAAI,QAAQ,KAAK,QAAQ;AACzB,QAAK,MAAM,YACP,IAAG,KAAK,SAAS,OAAO,EAAE;EAEjC;CACJ;AACD,iBAAc,UAAU,YAAY,WAAY;AAC5C,SAAO,KAAK;CACf;AACD,iBAAc,UAAU,qBAAqB,SAAU,MAAM;AACzD,OAAK,iBAAiB,SAAUE,SAAO,GAAG;AACtC,WAAM,UAAUA,QAAM,SAAS;EAClC,EAAC;EACF,SAAS,gBAAgB,KAAK;AAC1B,OAAI,WAAW;AACX,QAAI,UAAU,eAAe,IACzB,WAAU,UAAU;AAExB,cAAU,aAAa;GAC1B;EACJ;AACD,MAAI,KAAK,cACL,MAAK,IAAI,MAAM,GAAG,MAAM,KAAK,QAAQ,OAAO;GACxC,IAAI,KAAK,KAAK;AACd,OAAI,GAAG,WAAW,KAAK,MAAM,GAAG,UAAU,GAAG,aAAa;AACtD,SAAK,4BAA4B;AACjC;GACH;EACJ;EAEL,IAAI,YAAY;EAChB,IAAI,wBAAwB;EAC5B,IAAI;EACJ,IAAI;AACJ,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;GAC9B,IAAI,KAAK,KAAK;GACd,IAAI,SAAS,GAAG;GAChB,IAAI,aAAa;AACjB,OAAI,eAAe,QAAQ;AACvB,iBAAa;AACb,4BAAwB;GAC3B;AACD,OAAI,GAAG,aAAa;AAChB,YAAQ,KAAK,SAAS,SAAS,iBAAiB,KAAK,0BAA0B;AAC/E,UAAM,cAAc;AACpB,4BAAwB;GAC3B,MAEG,SAAQ,KAAK,SAAS,UAAU,wBAAwB,IAAI,2BAA2B,IAAI,KAAK,0BAA0B;AAE9H,QAAK,MAAM,YACP,UAAc,YAAY,SAAS,oCAAoC,MAAM,GAAG;AAEpF,OAAI,UAAU,WAAW;AACrB,UAAM,SAAS;AACf,QAAI,MAAM,iBAAiB,EACvB,OAAM,UAAU;AAEpB,UAAM,eAAe;AACrB,SAAK,MAAM,YACP,OAAM,cAAc;QAGpB,OAAM,cAAc;AAExB,oBAAgB,EAAE;AAClB,gBAAY;GACf;AACD,OAAK,GAAG,UAAU,eAAgB,GAAG,WAAW;AAC5C,UAAM,UAAU;AAChB,QAAI,MAAM,eAAe,MAAM,cAAc,EACzC,OAAM,cAAc;GAE3B;EACJ;AACD,kBAAgB,EAAE;AAClB,OAAK,iBAAiB,SAAUA,SAAO,GAAG;AACtC,QAAKA,QAAM,UAAU,QAAM,iBAAiB,GAAG,GAAG;AAC9C,YAAM,UAAU;AAChB,YAAM,eAAeA,QAAM,aAAaA,QAAM,cAAc;GAC/D;AACD,OAAIA,QAAM,WAAWA,QAAM,cAAc,EACrC,SAAM,cAAcA,QAAM;EAEjC,EAAC;CACL;AACD,iBAAc,UAAU,QAAQ,WAAY;AACxC,OAAK,iBAAiB,KAAK,YAAY;AACvC,SAAO;CACV;AACD,iBAAc,UAAU,cAAc,SAAU,OAAO;AACnD,QAAM,OAAO;CAChB;AACD,iBAAc,UAAU,qBAAqB,SAAU,iBAAiB;AACpE,OAAK,mBAAmB;AACxB,OAAU,KAAK,SAAS,SAAU,OAAO;AACrC,SAAM,cAAc;EACvB,EAAC;CACL;AACD,iBAAc,UAAU,cAAc,SAAU,QAAQ,QAAQ;AAC5D,MAAI,QAAQ;GACR,IAAI,cAAc,KAAK;AACvB,QAAK,YAAY,QACb,aAAY,UAAU;OAGtB,OAAW,YAAY,SAAS,QAAQ,KAAK;AAEjD,QAAK,IAAI,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;IAC9C,IAAI,UAAU,KAAK,YAAY;AAC/B,QAAI,YAAY,UAAU,YAAY,SAAS,0BAA0B;KACrE,IAAI,QAAQ,KAAK,QAAQ;AACzB,WAAW,OAAO,YAAY,SAAS,KAAK;IAC/C;GACJ;EACJ;CACJ;AACD,iBAAc,UAAU,WAAW,SAAU,QAAQ;EACjD,IAAI,SAAS,KAAK;EAClB,IAAI,aAAa,KAAK;EACtB,IAAI,QAAQ,OAAO;AACnB,OAAK,MACD;AAEJ,QAAM,IAAI,WAAW,YAAY,MAAM,IAAI;AAC3C,SAAO,OAAO;AACd,aAAW,OAAO,QAAa,YAAY,OAAO,EAAE,EAAE;CACzD;AACD,iBAAc,UAAU,SAAS,SAAU,OAAO,QAAQ;AACtD,OAAK,KAAK,SAAS,OAAO;AACtB,OAAI,SAAS,QAAQ,UAAU,KAC3B;AAEJ,QAAK,SAAS;AACd,QAAK,UAAU;AACf,QAAK,SAAS,cAAc,CAAC,OAAO,OAAO,OAAO;EACrD,OACI;GACD,IAAI,UAAU,KAAK;AACnB,WAAQ,MAAM,UAAU;GACxB,IAAI,OAAO,KAAK;GAChB,IAAI,OAAO,KAAK;AAChB,YAAS,SAAS,KAAK,QAAQ;AAC/B,aAAU,SAAS,KAAK,SAAS;AACjC,WAAQ,QAAQ,MAAM,GAAG,KAAK;AAC9B,YAAS,QAAQ,MAAM,GAAG,KAAK;AAC/B,WAAQ,MAAM,UAAU;AACxB,OAAI,KAAK,WAAW,SAAS,WAAW,KAAK,SAAS;AAClD,YAAQ,MAAM,QAAQ,QAAQ;AAC9B,YAAQ,MAAM,SAAS,SAAS;AAChC,SAAK,IAAI,MAAM,KAAK,QAChB,KAAI,KAAK,QAAQ,eAAe,GAAG,CAC/B,MAAK,QAAQ,IAAI,OAAO,OAAO,OAAO;AAG9C,SAAK,QAAQ,KAAK;GACrB;AACD,QAAK,SAAS;AACd,QAAK,UAAU;EAClB;AACD,SAAO;CACV;AACD,iBAAc,UAAU,aAAa,SAAU,QAAQ;EACnD,IAAI,QAAQ,KAAK,QAAQ;AACzB,MAAI,MACA,OAAM,OAAO;CAEpB;AACD,iBAAc,UAAU,UAAU,WAAY;AAC1C,OAAK,KAAK,YAAY;AACtB,OAAK,OACD,KAAK,UACD,KAAK,WACD,KAAK,UAAU;CAC9B;AACD,iBAAc,UAAU,oBAAoB,SAAU,MAAM;AACxD,SAAO,QAAQ,CAAE;AACjB,MAAI,KAAK,kBAAkB,KAAK,mBAC5B,QAAO,KAAK,QAAQ,eAAe;EAEvC,IAAI,aAAa,IAAIF,cAAM,SAAS,MAAM,KAAK,cAAc,KAAK;AAClE,aAAW,aAAa;AACxB,aAAW,MAAM,OAAO,KAAK,mBAAmB,KAAK,iBAAiB;EACtE,IAAI,MAAM,WAAW;AACrB,MAAI,KAAK,cAAc,KAAK,KAAK;AAC7B,QAAK,SAAS;GACd,IAAI,UAAU,WAAW,IAAI;GAC7B,IAAI,WAAW,WAAW,IAAI;AAC9B,QAAK,UAAU,SAAU,OAAO;AAC5B,QAAI,MAAM,YACN,KAAI,UAAU,MAAM,KAAK,GAAG,GAAG,SAAS,SAAS;aAE5C,MAAM,gBAAgB;AAC3B,SAAI,MAAM;AACV,WAAM,eAAe,IAAI;AACzB,SAAI,SAAS;IAChB;GACJ,EAAC;EACL,OACI;GACD,IAAI,QAAQ;IACR,SAAS;IACT,WAAW,KAAK;IAChB,YAAY,KAAK;GACpB;GACD,IAAI,cAAc,KAAK,QAAQ,eAAe,KAAK;AACnD,QAAK,IAAI,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;IACpD,IAAI,KAAK,YAAY;AACrB,UAAM,KAAK,IAAI,OAAO,MAAM,MAAM,EAAE;GACvC;EACJ;AACD,SAAO,WAAW;CACrB;AACD,iBAAc,UAAU,WAAW,WAAY;AAC3C,SAAO,KAAK;CACf;AACD,iBAAc,UAAU,YAAY,WAAY;AAC5C,SAAO,KAAK;CACf;AACD,QAAOD;AACV,GAAE;AACH,sBAAe;;;;ACnlBf,SAAgB,QAAQ,WAAW;AACjC,WAAU,gBAAgB,UAAUK,gBAAc;AACnD"}