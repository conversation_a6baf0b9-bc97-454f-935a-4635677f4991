import { Elysia, t } from 'elysia';
import { cron } from '@elysiajs/cron';
import { ScraperManager } from '../scrapers';

const scraperManager = new ScraperManager();

// 爬虫核心逻辑函数
async function runScraper() {
  console.log('Starting scheduled scraper job...');
  
  try {
    // 运行所有爬虫，每次爬取3页
    await scraperManager.runAllScrapers(3);
    console.log('Scheduled scraper job completed successfully');
  } catch (error) {
    console.error('Error in scheduled scraper job:', error);
  }
}

// 手动触发爬虫的函数
async function runManualScraper(source?: string, pages: number = 1) {
  console.log(`Starting manual scraper job for ${source || 'all sources'}...`);
  
  try {
    if (source === 'beike') {
      await scraperManager.runBeikeOnly(pages);
    } else if (source === 'anjuke') {
      await scraperManager.runAnjukeOnly(pages);
    } else {
      await scraperManager.runAllScrapers(pages);
    }
    console.log('Manual scraper job completed successfully');
  } catch (error) {
    console.error('Error in manual scraper job:', error);
    throw error;
  }
}

export const scraperTasks = new Elysia({ prefix: '/api' })
  // 定时任务：每6小时执行一次
  .use(
    cron({
      name: 'housing-scraper',
      // 每6小时执行一次 (0分钟，每6小时，每天，每月，每周)
      pattern: '0 */6 * * *', 
      async run() {
        await runScraper();
      }
    })
  )
  
  // 手动触发爬虫的API端点
  .post('/scraper/run', async ({ body }) => {
    try {
      const { source, pages = 1 } = body || {};
      await runManualScraper(source, pages);

      return {
        success: true,
        message: `Scraper job completed for ${source || 'all sources'}`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Manual scraper API error:', error);
      return {
        success: false,
        message: 'Scraper job failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      };
    }
  }, {
    body: t.Object({
      source: t.Optional(t.String()),
      pages: t.Optional(t.Number())
    })
  })
  
  // 获取爬虫状态
  .get('/scraper/status', async () => {
    try {
      // 这里可以添加更多状态信息，比如最后运行时间、运行状态等
      return {
        status: 'active',
        lastRun: new Date().toISOString(),
        nextRun: 'Every 6 hours',
        availableSources: ['beike', 'anjuke']
      };
    } catch (error) {
      console.error('Error getting scraper status:', error);
      return {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

// 导出手动运行函数供其他模块使用
export { runManualScraper, runScraper };
