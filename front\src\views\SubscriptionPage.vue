<template>
  <div class="subscription-page">
    <div class="page-container">
      <!-- 页面标题和操作 -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">订阅管理</h1>
          <div class="header-actions">
            <a-space>
              <a-button @click="handleRefresh" :loading="loading">
                <template #icon>
                  <icon-refresh />
                </template>
                刷新
              </a-button>
              <a-button type="primary" @click="showCreateModal = true">
                <template #icon>
                  <icon-plus />
                </template>
                新建订阅
              </a-button>
            </a-space>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <a-row :gutter="24" class="stats-row">
        <a-col :span="8">
          <a-card class="stat-card">
            <a-statistic
              title="总订阅数"
              :value="subscriptions.length"
              :value-style="{ color: '#165dff' }"
            >
              <template #prefix>
                <icon-notification />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="stat-card">
            <a-statistic
              title="活跃订阅"
              :value="activeSubscriptions.length"
              :value-style="{ color: '#00b42a' }"
            >
              <template #prefix>
                <icon-check-circle />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="stat-card">
            <a-statistic
              title="价格监控"
              :value="priceChangeSubscriptions.length"
              :value-style="{ color: '#f53f3f' }"
            >
              <template #prefix>
                <icon-heart />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>

      <!-- 订阅列表 -->
      <a-card title="我的订阅" class="subscription-list-card">
        <template #extra>
          <a-space>
            <a-select
              v-model="filterType"
              placeholder="筛选类型"
              style="width: 150px"
              @change="handleFilterChange"
            >
              <a-option value="">全部</a-option>
              <a-option value="NEW_LISTING">新房源</a-option>
              <a-option value="PRICE_CHANGE">价格变动</a-option>
            </a-select>
            <a-select
              v-model="filterActive"
              placeholder="筛选状态"
              style="width: 120px"
              @change="handleFilterChange"
            >
              <a-option value="">全部</a-option>
              <a-option value="true">活跃</a-option>
              <a-option value="false">已停用</a-option>
            </a-select>
          </a-space>
        </template>

        <a-spin :loading="loading" style="width: 100%">
          <div v-if="hasSubscriptions">
            <a-list
              :data="subscriptions"
              :pagination="{
                current: pagination.page,
                total: pagination.total,
                pageSize: pagination.limit,
                showTotal: true,
                showJumper: true,
                onChange: handlePageChange
              }"
            >
              <template #item="{ item }">
                <a-list-item class="subscription-item">
                  <div class="subscription-content">
                    <div class="subscription-info">
                      <div class="subscription-header">
                        <a-space>
                          <a-tag :color="getTypeColor(item.subscriptionType)">
                            {{ getTypeName(item.subscriptionType) }}
                          </a-tag>
                          <a-tag
                            :color="item.isActive ? 'green' : 'gray'"
                            :bordered="false"
                          >
                            {{ item.isActive ? '活跃' : '已停用' }}
                          </a-tag>
                        </a-space>
                      </div>
                      
                      <div class="subscription-details">
                        <div v-if="item.subscriptionType === 'NEW_LISTING'" class="keywords">
                          <strong>关键词:</strong> {{ item.keywords || '无' }}
                        </div>
                        <div v-else-if="item.listing" class="listing-info">
                          <strong>监控房源:</strong>
                          <router-link
                            :to="`/listings/${item.listing.id}`"
                            class="listing-link"
                          >
                            {{ item.listing.title }}
                          </router-link>
                          <div class="listing-meta">
                            {{ item.listing.district }} · {{ item.listing.community }} ·
                            {{ item.listing.totalPrice }}万
                          </div>
                        </div>
                        
                        <div class="subscription-time">
                          <span>创建时间: {{ formatTime(item.createdAt) }}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div class="subscription-actions">
                      <a-space>
                        <a-button
                          type="text"
                          size="small"
                          @click="handleToggleStatus(item)"
                        >
                          {{ item.isActive ? '停用' : '启用' }}
                        </a-button>
                        <a-button
                          type="text"
                          size="small"
                          @click="handleEdit(item)"
                        >
                          编辑
                        </a-button>
                        <a-popconfirm
                          content="确定要删除这个订阅吗？"
                          @ok="handleDelete(item.id)"
                        >
                          <a-button
                            type="text"
                            size="small"
                            status="danger"
                          >
                            删除
                          </a-button>
                        </a-popconfirm>
                      </a-space>
                    </div>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </div>
          <a-empty v-else description="暂无订阅" />
        </a-spin>
      </a-card>
    </div>

    <!-- 创建订阅弹窗 -->
    <a-modal
      v-model:visible="showCreateModal"
      title="创建订阅"
      width="500px"
      @ok="handleCreateSubmit"
      @cancel="handleCreateCancel"
    >
      <a-form
        ref="createFormRef"
        :model="createForm"
        layout="vertical"
      >
        <a-form-item
          label="订阅类型"
          field="subscriptionType"
          :rules="[{ required: true, message: '请选择订阅类型' }]"
        >
          <a-radio-group v-model="createForm.subscriptionType">
            <a-radio value="NEW_LISTING">新房源通知</a-radio>
            <a-radio value="PRICE_CHANGE">价格变动监控</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item
          v-if="createForm.subscriptionType === 'NEW_LISTING'"
          label="关键词"
          field="keywords"
          :rules="[{ required: true, message: '请输入关键词' }]"
        >
          <a-input
            v-model="createForm.keywords"
            placeholder="请输入关键词，如：学区房、地铁房等"
          />
        </a-form-item>

        <a-form-item
          v-if="createForm.subscriptionType === 'PRICE_CHANGE'"
          label="房源ID"
          field="listingId"
          :rules="[{ required: true, message: '请输入房源ID' }]"
        >
          <a-input
            v-model="createForm.listingId"
            placeholder="请输入要监控的房源ID"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑订阅弹窗 -->
    <a-modal
      v-model:visible="showEditModal"
      title="编辑订阅"
      width="500px"
      @ok="handleEditSubmit"
      @cancel="handleEditCancel"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        layout="vertical"
      >
        <a-form-item
          v-if="editingSubscription?.subscriptionType === 'NEW_LISTING'"
          label="关键词"
          field="keywords"
        >
          <a-input
            v-model="editForm.keywords"
            placeholder="请输入关键词"
          />
        </a-form-item>

        <a-form-item label="状态" field="isActive">
          <a-switch
            v-model="editForm.isActive"
            checked-text="启用"
            unchecked-text="停用"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import {
  IconRefresh,
  IconPlus,
  IconNotification,
  IconCheckCircle,
  IconHeart
} from '@arco-design/web-vue/es/icon';
import { useSubscriptionsStore } from '../store/subscriptions';
import type { Subscription } from '../api/client';

const subscriptionsStore = useSubscriptionsStore();

// 状态
const showCreateModal = ref(false);
const showEditModal = ref(false);
const editingSubscription = ref<Subscription | null>(null);
const filterType = ref('');
const filterActive = ref('');

// 表单引用
const createFormRef = ref();
const editFormRef = ref();

// 表单数据
const createForm = ref({
  subscriptionType: 'NEW_LISTING' as 'NEW_LISTING' | 'PRICE_CHANGE',
  keywords: '',
  listingId: ''
});

const editForm = ref({
  keywords: '',
  isActive: true
});

// 计算属性
const subscriptions = computed(() => subscriptionsStore.subscriptions);
const loading = computed(() => subscriptionsStore.loading);
const pagination = computed(() => subscriptionsStore.pagination);
const hasSubscriptions = computed(() => subscriptionsStore.hasSubscriptions);
const activeSubscriptions = computed(() => subscriptionsStore.activeSubscriptions);
const priceChangeSubscriptions = computed(() => subscriptionsStore.priceChangeSubscriptions);

// 获取订阅类型颜色
const getTypeColor = (type: string) => {
  switch (type) {
    case 'NEW_LISTING':
      return 'blue';
    case 'PRICE_CHANGE':
      return 'red';
    default:
      return 'gray';
  }
};

// 获取订阅类型名称
const getTypeName = (type: string) => {
  switch (type) {
    case 'NEW_LISTING':
      return '新房源通知';
    case 'PRICE_CHANGE':
      return '价格变动';
    default:
      return type;
  }
};

// 格式化时间
const formatTime = (timeStr: string): string => {
  return new Date(timeStr).toLocaleString();
};

// 事件处理
const handleRefresh = () => {
  loadSubscriptions();
};

const handleFilterChange = () => {
  const params: any = {};
  if (filterType.value) params.type = filterType.value;
  if (filterActive.value) params.active = filterActive.value === 'true';
  
  subscriptionsStore.fetchSubscriptions(params);
};

const handlePageChange = (page: number) => {
  const params: any = { page };
  if (filterType.value) params.type = filterType.value;
  if (filterActive.value) params.active = filterActive.value === 'true';
  
  subscriptionsStore.fetchSubscriptions(params);
};

const handleToggleStatus = async (subscription: Subscription) => {
  try {
    await subscriptionsStore.toggleSubscriptionStatus(subscription.id);
    Message.success(`订阅已${subscription.isActive ? '停用' : '启用'}`);
  } catch (error) {
    Message.error('操作失败，请重试');
  }
};

const handleEdit = (subscription: Subscription) => {
  editingSubscription.value = subscription;
  editForm.value = {
    keywords: subscription.keywords || '',
    isActive: subscription.isActive
  };
  showEditModal.value = true;
};

const handleDelete = async (id: string) => {
  try {
    await subscriptionsStore.deleteSubscription(id);
    Message.success('订阅已删除');
  } catch (error) {
    Message.error('删除失败，请重试');
  }
};

const handleCreateSubmit = async () => {
  try {
    const valid = await createFormRef.value?.validate();
    if (!valid) return;

    const data: any = {
      subscriptionType: createForm.value.subscriptionType
    };

    if (createForm.value.subscriptionType === 'NEW_LISTING') {
      data.keywords = createForm.value.keywords;
    } else {
      data.listingId = createForm.value.listingId;
    }

    await subscriptionsStore.createSubscription(data);
    showCreateModal.value = false;
    resetCreateForm();
    loadSubscriptions();
    Message.success('订阅创建成功');
  } catch (error) {
    Message.error('创建失败，请重试');
  }
};

const handleCreateCancel = () => {
  showCreateModal.value = false;
  resetCreateForm();
};

const handleEditSubmit = async () => {
  try {
    if (!editingSubscription.value) return;

    await subscriptionsStore.updateSubscription(editingSubscription.value.id, {
      keywords: editForm.value.keywords,
      isActive: editForm.value.isActive
    });

    showEditModal.value = false;
    editingSubscription.value = null;
    loadSubscriptions();
    Message.success('订阅更新成功');
  } catch (error) {
    Message.error('更新失败，请重试');
  }
};

const handleEditCancel = () => {
  showEditModal.value = false;
  editingSubscription.value = null;
};

const resetCreateForm = () => {
  createForm.value = {
    subscriptionType: 'NEW_LISTING',
    keywords: '',
    listingId: ''
  };
};

// 加载订阅数据
const loadSubscriptions = () => {
  const params: any = {};
  if (filterType.value) params.type = filterType.value;
  if (filterActive.value) params.active = filterActive.value === 'true';
  
  subscriptionsStore.fetchSubscriptions(params);
};

// 页面挂载时加载数据
onMounted(() => {
  loadSubscriptions();
});
</script>

<style scoped>
.subscription-page {
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e5e6eb;
  padding: 24px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
  margin: 0;
}

.stats-row {
  margin: 24px 0;
}

.stat-card {
  text-align: center;
}

.subscription-list-card {
  margin-bottom: 24px;
}

.subscription-item {
  border-bottom: 1px solid #f2f3f5;
}

.subscription-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.subscription-info {
  flex: 1;
}

.subscription-header {
  margin-bottom: 12px;
}

.subscription-details {
  color: #4e5969;
}

.keywords {
  margin-bottom: 8px;
}

.listing-info {
  margin-bottom: 8px;
}

.listing-link {
  color: #165dff;
  text-decoration: none;
  margin-left: 8px;
}

.listing-link:hover {
  text-decoration: underline;
}

.listing-meta {
  color: #86909c;
  font-size: 12px;
  margin-top: 4px;
}

.subscription-time {
  font-size: 12px;
  color: #86909c;
}

.subscription-actions {
  flex-shrink: 0;
  margin-left: 16px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .subscription-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .subscription-actions {
    margin-left: 0;
  }
}
</style>
