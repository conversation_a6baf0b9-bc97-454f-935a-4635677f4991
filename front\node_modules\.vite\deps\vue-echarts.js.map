{"version": 3, "file": "vue-echarts.js", "names": [], "sources": ["../../.pnpm/vue-demi@0.13.11_vue@3.5.17_typescript@5.8.3_/node_modules/vue-demi/lib/index.mjs", "../../.pnpm/vue-echarts@7.0.3_@vue+runt_b05719f8e2da0186bc834b493a74e30b/node_modules/vue-echarts/dist/index.js"], "sourcesContent": ["import * as Vue from 'vue'\n\nvar isVue2 = false\nvar isVue3 = true\nvar Vue2 = undefined\n\nfunction install() {}\n\nexport function set(target, key, val) {\n  if (Array.isArray(target)) {\n    target.length = Math.max(target.length, key)\n    target.splice(key, 1, val)\n    return val\n  }\n  target[key] = val\n  return val\n}\n\nexport function del(target, key) {\n  if (Array.isArray(target)) {\n    target.splice(key, 1)\n    return\n  }\n  delete target[key]\n}\n\nexport * from 'vue'\nexport {\n  Vue,\n  Vue2,\n  isVue2,\n  isVue3,\n  install,\n}\n", "import { watch, isRef, unref, inject, computed, watchEffect, Vue2, defineComponent, shallowRef, toRefs, getCurrentInstance, onMounted, onBeforeUnmount, h, nextTick } from 'vue-demi';\nimport { throttle, init } from 'echarts/core';\n\nconst METHOD_NAMES = [\n  \"getWidth\",\n  \"getHeight\",\n  \"getDom\",\n  \"getOption\",\n  \"resize\",\n  \"dispatchAction\",\n  \"convertToPixel\",\n  \"convertFromPixel\",\n  \"containPixel\",\n  \"getDataURL\",\n  \"getConnectedDataURL\",\n  \"appendData\",\n  \"clear\",\n  \"isDisposed\",\n  \"dispose\"\n];\nfunction usePublicAPI(chart) {\n  function makePublicMethod(name) {\n    return (...args) => {\n      if (!chart.value) {\n        throw new Error(\"ECharts is not initialized yet.\");\n      }\n      return chart.value[name].apply(chart.value, args);\n    };\n  }\n  function makePublicMethods() {\n    const methods = /* @__PURE__ */ Object.create(null);\n    METHOD_NAMES.forEach((name) => {\n      methods[name] = makePublicMethod(name);\n    });\n    return methods;\n  }\n  return makePublicMethods();\n}\n\nfunction useAutoresize(chart, autoresize, root) {\n  watch(\n    [root, chart, autoresize],\n    ([root2, chart2, autoresize2], _, onCleanup) => {\n      let ro = null;\n      if (root2 && chart2 && autoresize2) {\n        const { offsetWidth, offsetHeight } = root2;\n        const autoresizeOptions = autoresize2 === true ? {} : autoresize2;\n        const { throttle: wait = 100, onResize } = autoresizeOptions;\n        let initialResizeTriggered = false;\n        const callback = () => {\n          chart2.resize();\n          onResize?.();\n        };\n        const resizeCallback = wait ? throttle(callback, wait) : callback;\n        ro = new ResizeObserver(() => {\n          if (!initialResizeTriggered) {\n            initialResizeTriggered = true;\n            if (root2.offsetWidth === offsetWidth && root2.offsetHeight === offsetHeight) {\n              return;\n            }\n          }\n          resizeCallback();\n        });\n        ro.observe(root2);\n      }\n      onCleanup(() => {\n        if (ro) {\n          ro.disconnect();\n          ro = null;\n        }\n      });\n    }\n  );\n}\nconst autoresizeProps = {\n  autoresize: [Boolean, Object]\n};\n\nconst onRE = /^on[^a-z]/;\nconst isOn = (key) => onRE.test(key);\nfunction omitOn(attrs) {\n  const result = {};\n  for (const key in attrs) {\n    if (!isOn(key)) {\n      result[key] = attrs[key];\n    }\n  }\n  return result;\n}\nfunction unwrapInjected(injection, defaultValue) {\n  const value = isRef(injection) ? unref(injection) : injection;\n  if (value && typeof value === \"object\" && \"value\" in value) {\n    return value.value || defaultValue;\n  }\n  return value || defaultValue;\n}\n\nconst LOADING_OPTIONS_KEY = \"ecLoadingOptions\";\nfunction useLoading(chart, loading, loadingOptions) {\n  const defaultLoadingOptions = inject(LOADING_OPTIONS_KEY, {});\n  const realLoadingOptions = computed(() => ({\n    ...unwrapInjected(defaultLoadingOptions, {}),\n    ...loadingOptions?.value\n  }));\n  watchEffect(() => {\n    const instance = chart.value;\n    if (!instance) {\n      return;\n    }\n    if (loading.value) {\n      instance.showLoading(realLoadingOptions.value);\n    } else {\n      instance.hideLoading();\n    }\n  });\n}\nconst loadingProps = {\n  loading: Boolean,\n  loadingOptions: Object\n};\n\nlet registered = null;\nconst TAG_NAME = \"x-vue-echarts\";\nfunction register() {\n  if (registered != null) {\n    return registered;\n  }\n  if (typeof HTMLElement === \"undefined\" || typeof customElements === \"undefined\") {\n    return registered = false;\n  }\n  try {\n    const reg = new Function(\n      \"tag\",\n      // Use esbuild repl to keep build process simple\n      // https://esbuild.github.io/try/#dAAwLjIzLjAALS1taW5pZnkAY2xhc3MgRUNoYXJ0c0VsZW1lbnQgZXh0ZW5kcyBIVE1MRWxlbWVudCB7CiAgX19kaXNwb3NlID0gbnVsbDsKCiAgZGlzY29ubmVjdGVkQ2FsbGJhY2soKSB7CiAgICBpZiAodGhpcy5fX2Rpc3Bvc2UpIHsKICAgICAgdGhpcy5fX2Rpc3Bvc2UoKTsKICAgICAgdGhpcy5fX2Rpc3Bvc2UgPSBudWxsOwogICAgfQogIH0KfQoKaWYgKGN1c3RvbUVsZW1lbnRzLmdldCh0YWcpID09IG51bGwpIHsKICBjdXN0b21FbGVtZW50cy5kZWZpbmUodGFnLCBFQ2hhcnRzRWxlbWVudCk7Cn0K\n      \"class EChartsElement extends HTMLElement{__dispose=null;disconnectedCallback(){this.__dispose&&(this.__dispose(),this.__dispose=null)}}customElements.get(tag)==null&&customElements.define(tag,EChartsElement);\"\n    );\n    reg(TAG_NAME);\n  } catch (e) {\n    return registered = false;\n  }\n  return registered = true;\n}\n\ndocument.head.appendChild(document.createElement('style')).textContent=\"x-vue-echarts{display:block;width:100%;height:100%;min-width:0}\\n\";\n\nconst wcRegistered = register();\nif (Vue2) {\n  Vue2.config.ignoredElements.push(TAG_NAME);\n}\nconst THEME_KEY = \"ecTheme\";\nconst INIT_OPTIONS_KEY = \"ecInitOptions\";\nconst UPDATE_OPTIONS_KEY = \"ecUpdateOptions\";\nconst NATIVE_EVENT_RE = /(^&?~?!?)native:/;\nvar ECharts = defineComponent({\n  name: \"echarts\",\n  props: {\n    option: Object,\n    theme: {\n      type: [Object, String]\n    },\n    initOptions: Object,\n    updateOptions: Object,\n    group: String,\n    manualUpdate: Boolean,\n    ...autoresizeProps,\n    ...loadingProps\n  },\n  emits: {},\n  inheritAttrs: false,\n  setup(props, { attrs }) {\n    const root = shallowRef();\n    const chart = shallowRef();\n    const manualOption = shallowRef();\n    const defaultTheme = inject(THEME_KEY, null);\n    const defaultInitOptions = inject(INIT_OPTIONS_KEY, null);\n    const defaultUpdateOptions = inject(UPDATE_OPTIONS_KEY, null);\n    const { autoresize, manualUpdate, loading, loadingOptions } = toRefs(props);\n    const realOption = computed(\n      () => manualOption.value || props.option || null\n    );\n    const realTheme = computed(\n      () => props.theme || unwrapInjected(defaultTheme, {})\n    );\n    const realInitOptions = computed(\n      () => props.initOptions || unwrapInjected(defaultInitOptions, {})\n    );\n    const realUpdateOptions = computed(\n      () => props.updateOptions || unwrapInjected(defaultUpdateOptions, {})\n    );\n    const nonEventAttrs = computed(() => omitOn(attrs));\n    const nativeListeners = {};\n    const listeners = getCurrentInstance().proxy.$listeners;\n    const realListeners = {};\n    if (!listeners) {\n      Object.keys(attrs).filter((key) => isOn(key)).forEach((key) => {\n        let event = key.charAt(2).toLowerCase() + key.slice(3);\n        if (event.indexOf(\"native:\") === 0) {\n          const nativeKey = `on${event.charAt(7).toUpperCase()}${event.slice(\n            8\n          )}`;\n          nativeListeners[nativeKey] = attrs[key];\n          return;\n        }\n        if (event.substring(event.length - 4) === \"Once\") {\n          event = `~${event.substring(0, event.length - 4)}`;\n        }\n        realListeners[event] = attrs[key];\n      });\n    } else {\n      Object.keys(listeners).forEach((key) => {\n        if (NATIVE_EVENT_RE.test(key)) {\n          nativeListeners[key.replace(NATIVE_EVENT_RE, \"$1\")] = listeners[key];\n        } else {\n          realListeners[key] = listeners[key];\n        }\n      });\n    }\n    function init$1(option) {\n      if (!root.value) {\n        return;\n      }\n      const instance = chart.value = init(\n        root.value,\n        realTheme.value,\n        realInitOptions.value\n      );\n      if (props.group) {\n        instance.group = props.group;\n      }\n      Object.keys(realListeners).forEach((key) => {\n        let handler = realListeners[key];\n        if (!handler) {\n          return;\n        }\n        let event = key.toLowerCase();\n        if (event.charAt(0) === \"~\") {\n          event = event.substring(1);\n          handler.__once__ = true;\n        }\n        let target = instance;\n        if (event.indexOf(\"zr:\") === 0) {\n          target = instance.getZr();\n          event = event.substring(3);\n        }\n        if (handler.__once__) {\n          delete handler.__once__;\n          const raw = handler;\n          handler = (...args) => {\n            raw(...args);\n            target.off(event, handler);\n          };\n        }\n        target.on(event, handler);\n      });\n      function resize() {\n        if (instance && !instance.isDisposed()) {\n          instance.resize();\n        }\n      }\n      function commit() {\n        const opt = option || realOption.value;\n        if (opt) {\n          instance.setOption(opt, realUpdateOptions.value);\n        }\n      }\n      if (autoresize.value) {\n        nextTick(() => {\n          resize();\n          commit();\n        });\n      } else {\n        commit();\n      }\n    }\n    function setOption(option, updateOptions) {\n      if (props.manualUpdate) {\n        manualOption.value = option;\n      }\n      if (!chart.value) {\n        init$1(option);\n      } else {\n        chart.value.setOption(option, updateOptions || {});\n      }\n    }\n    function cleanup() {\n      if (chart.value) {\n        chart.value.dispose();\n        chart.value = void 0;\n      }\n    }\n    let unwatchOption = null;\n    watch(\n      manualUpdate,\n      (manualUpdate2) => {\n        if (typeof unwatchOption === \"function\") {\n          unwatchOption();\n          unwatchOption = null;\n        }\n        if (!manualUpdate2) {\n          unwatchOption = watch(\n            () => props.option,\n            (option, oldOption) => {\n              if (!option) {\n                return;\n              }\n              if (!chart.value) {\n                init$1();\n              } else {\n                chart.value.setOption(option, {\n                  // mutating `option` will lead to `notMerge: false` and\n                  // replacing it with new reference will lead to `notMerge: true`\n                  notMerge: option !== oldOption,\n                  ...realUpdateOptions.value\n                });\n              }\n            },\n            { deep: true }\n          );\n        }\n      },\n      {\n        immediate: true\n      }\n    );\n    watch(\n      [realTheme, realInitOptions],\n      () => {\n        cleanup();\n        init$1();\n      },\n      {\n        deep: true\n      }\n    );\n    watchEffect(() => {\n      if (props.group && chart.value) {\n        chart.value.group = props.group;\n      }\n    });\n    const publicApi = usePublicAPI(chart);\n    useLoading(chart, loading, loadingOptions);\n    useAutoresize(chart, autoresize, root);\n    onMounted(() => {\n      init$1();\n    });\n    onBeforeUnmount(() => {\n      if (wcRegistered && root.value) {\n        root.value.__dispose = cleanup;\n      } else {\n        cleanup();\n      }\n    });\n    return {\n      chart,\n      root,\n      setOption,\n      nonEventAttrs,\n      nativeListeners,\n      ...publicApi\n    };\n  },\n  render() {\n    const attrs = Vue2 ? { attrs: this.nonEventAttrs, on: this.nativeListeners } : { ...this.nonEventAttrs, ...this.nativeListeners };\n    attrs.ref = \"root\";\n    attrs.class = attrs.class ? [\"echarts\"].concat(attrs.class) : \"echarts\";\n    return h(TAG_NAME, attrs);\n  }\n});\n\nexport { INIT_OPTIONS_KEY, LOADING_OPTIONS_KEY, THEME_KEY, UPDATE_OPTIONS_KEY, ECharts as default };\n//# sourceMappingURL=index.js.map\n"], "x_google_ignoreList": [0, 1], "mappings": ";;;;;;;AAIA,IAAI;;;;ACDJ,MAAM,eAAe;CACnB;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACD;AACD,SAAS,aAAa,OAAO;CAC3B,SAAS,iBAAiB,MAAM;AAC9B,SAAO,CAAC,GAAG,SAAS;AAClB,QAAK,MAAM,MACT,OAAM,IAAI,MAAM;AAElB,UAAO,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,KAAK;EAClD;CACF;CACD,SAAS,oBAAoB;EAC3B,MAAM,0BAA0B,OAAO,OAAO,KAAK;AACnD,eAAa,QAAQ,CAAC,SAAS;AAC7B,WAAQ,QAAQ,iBAAiB,KAAK;EACvC,EAAC;AACF,SAAO;CACR;AACD,QAAO,mBAAmB;AAC3B;AAED,SAAS,cAAc,OAAO,YAAY,MAAM;AAC9C,OACE;EAAC;EAAM;EAAO;CAAW,GACzB,CAAC,CAAC,OAAO,QAAQ,YAAY,EAAE,GAAG,cAAc;EAC9C,IAAI,KAAK;AACT,MAAI,SAAS,UAAU,aAAa;GAClC,MAAM,EAAE,aAAa,cAAc,GAAG;GACtC,MAAM,oBAAoB,gBAAgB,OAAO,CAAE,IAAG;GACtD,MAAM,EAAE,UAAU,OAAO,KAAK,UAAU,GAAG;GAC3C,IAAI,yBAAyB;GAC7B,MAAM,WAAW,MAAM;AACrB,WAAO,QAAQ;AACf,gBAAY;GACb;GACD,MAAM,iBAAiB,OAAO,SAAS,UAAU,KAAK,GAAG;AACzD,QAAK,IAAI,eAAe,MAAM;AAC5B,SAAK,wBAAwB;AAC3B,8BAAyB;AACzB,SAAI,MAAM,gBAAgB,eAAe,MAAM,iBAAiB,aAC9D;IAEH;AACD,oBAAgB;GACjB;AACD,MAAG,QAAQ,MAAM;EAClB;AACD,YAAU,MAAM;AACd,OAAI,IAAI;AACN,OAAG,YAAY;AACf,SAAK;GACN;EACF,EAAC;CACH,EACF;AACF;AACD,MAAM,kBAAkB,EACtB,YAAY,CAAC,SAAS,MAAO,EAC9B;AAED,MAAM,OAAO;AACb,MAAM,OAAO,CAAC,QAAQ,KAAK,KAAK,IAAI;AACpC,SAAS,OAAO,OAAO;CACrB,MAAM,SAAS,CAAE;AACjB,MAAK,MAAM,OAAO,MAChB,MAAK,KAAK,IAAI,CACZ,QAAO,OAAO,MAAM;AAGxB,QAAO;AACR;AACD,SAAS,eAAe,WAAW,cAAc;CAC/C,MAAM,QAAQ,MAAM,UAAU,GAAG,MAAM,UAAU,GAAG;AACpD,KAAI,gBAAgB,UAAU,YAAY,WAAW,MACnD,QAAO,MAAM,SAAS;AAExB,QAAO,SAAS;AACjB;AAED,MAAM,sBAAsB;AAC5B,SAAS,WAAW,OAAO,SAAS,gBAAgB;CAClD,MAAM,wBAAwB,OAAO,qBAAqB,CAAE,EAAC;CAC7D,MAAM,qBAAqB,SAAS,OAAO;EACzC,GAAG,eAAe,uBAAuB,CAAE,EAAC;EAC5C,GAAG,gBAAgB;CACpB,GAAE;AACH,aAAY,MAAM;EAChB,MAAM,WAAW,MAAM;AACvB,OAAK,SACH;AAEF,MAAI,QAAQ,MACV,UAAS,YAAY,mBAAmB,MAAM;MAE9C,UAAS,aAAa;CAEzB,EAAC;AACH;AACD,MAAM,eAAe;CACnB,SAAS;CACT,gBAAgB;AACjB;AAED,IAAI,aAAa;AACjB,MAAM,WAAW;AACjB,SAAS,WAAW;AAClB,KAAI,cAAc,KAChB,QAAO;AAET,YAAW,gBAAgB,sBAAsB,mBAAmB,YAClE,QAAO,aAAa;AAEtB,KAAI;EACF,MAAM,MAAM,IAAI,SACd,OAGA;AAEF,MAAI,SAAS;CACd,SAAQ,GAAG;AACV,SAAO,aAAa;CACrB;AACD,QAAO,aAAa;AACrB;AAED,SAAS,KAAK,YAAY,SAAS,cAAc,QAAQ,CAAC,CAAC,cAAY;AAEvE,MAAM,eAAe,UAAU;AAC/B,IAAI,KACF,MAAK,OAAO,gBAAgB,KAAK,SAAS;AAE5C,MAAM,YAAY;AAClB,MAAM,mBAAmB;AACzB,MAAM,qBAAqB;AAC3B,MAAM,kBAAkB;AACxB,IAAI,UAAU,gBAAgB;CAC5B,MAAM;CACN,OAAO;EACL,QAAQ;EACR,OAAO,EACL,MAAM,CAAC,QAAQ,MAAO,EACvB;EACD,aAAa;EACb,eAAe;EACf,OAAO;EACP,cAAc;EACd,GAAG;EACH,GAAG;CACJ;CACD,OAAO,CAAE;CACT,cAAc;CACd,MAAM,OAAO,EAAE,OAAO,EAAE;EACtB,MAAM,OAAO,YAAY;EACzB,MAAM,QAAQ,YAAY;EAC1B,MAAM,eAAe,YAAY;EACjC,MAAM,eAAe,OAAO,WAAW,KAAK;EAC5C,MAAM,qBAAqB,OAAO,kBAAkB,KAAK;EACzD,MAAM,uBAAuB,OAAO,oBAAoB,KAAK;EAC7D,MAAM,EAAE,YAAY,cAAc,SAAS,gBAAgB,GAAG,OAAO,MAAM;EAC3E,MAAM,aAAa,SACjB,MAAM,aAAa,SAAS,MAAM,UAAU,KAC7C;EACD,MAAM,YAAY,SAChB,MAAM,MAAM,SAAS,eAAe,cAAc,CAAE,EAAC,CACtD;EACD,MAAM,kBAAkB,SACtB,MAAM,MAAM,eAAe,eAAe,oBAAoB,CAAE,EAAC,CAClE;EACD,MAAM,oBAAoB,SACxB,MAAM,MAAM,iBAAiB,eAAe,sBAAsB,CAAE,EAAC,CACtE;EACD,MAAM,gBAAgB,SAAS,MAAM,OAAO,MAAM,CAAC;EACnD,MAAM,kBAAkB,CAAE;EAC1B,MAAM,YAAY,oBAAoB,CAAC,MAAM;EAC7C,MAAM,gBAAgB,CAAE;AACxB,OAAK,UACH,QAAO,KAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,QAAQ,CAAC,QAAQ;GAC7D,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC,aAAa,GAAG,IAAI,MAAM,EAAE;AACtD,OAAI,MAAM,QAAQ,UAAU,KAAK,GAAG;IAClC,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE,CAAC,aAAa,GAAG,MAAM,MAC3D,EACD,EAAE;AACH,oBAAgB,aAAa,MAAM;AACnC;GACD;AACD,OAAI,MAAM,UAAU,MAAM,SAAS,EAAE,KAAK,OACxC,SAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,GAAG,MAAM,SAAS,EAAE,EAAE;AAEpD,iBAAc,SAAS,MAAM;EAC9B,EAAC;MAEF,QAAO,KAAK,UAAU,CAAC,QAAQ,CAAC,QAAQ;AACtC,OAAI,gBAAgB,KAAK,IAAI,CAC3B,iBAAgB,IAAI,QAAQ,iBAAiB,KAAK,IAAI,UAAU;OAEhE,eAAc,OAAO,UAAU;EAElC,EAAC;EAEJ,SAAS,OAAO,QAAQ;AACtB,QAAK,KAAK,MACR;GAEF,MAAM,WAAW,MAAM,QAAQ,KAC7B,KAAK,OACL,UAAU,OACV,gBAAgB,MACjB;AACD,OAAI,MAAM,MACR,UAAS,QAAQ,MAAM;AAEzB,UAAO,KAAK,cAAc,CAAC,QAAQ,CAAC,QAAQ;IAC1C,IAAI,UAAU,cAAc;AAC5B,SAAK,QACH;IAEF,IAAI,QAAQ,IAAI,aAAa;AAC7B,QAAI,MAAM,OAAO,EAAE,KAAK,KAAK;AAC3B,aAAQ,MAAM,UAAU,EAAE;AAC1B,aAAQ,WAAW;IACpB;IACD,IAAI,SAAS;AACb,QAAI,MAAM,QAAQ,MAAM,KAAK,GAAG;AAC9B,cAAS,SAAS,OAAO;AACzB,aAAQ,MAAM,UAAU,EAAE;IAC3B;AACD,QAAI,QAAQ,UAAU;AACpB,YAAO,QAAQ;KACf,MAAM,MAAM;AACZ,eAAU,CAAC,GAAG,SAAS;AACrB,UAAI,GAAG,KAAK;AACZ,aAAO,IAAI,OAAO,QAAQ;KAC3B;IACF;AACD,WAAO,GAAG,OAAO,QAAQ;GAC1B,EAAC;GACF,SAAS,SAAS;AAChB,QAAI,aAAa,SAAS,YAAY,CACpC,UAAS,QAAQ;GAEpB;GACD,SAAS,SAAS;IAChB,MAAM,MAAM,UAAU,WAAW;AACjC,QAAI,IACF,UAAS,UAAU,KAAK,kBAAkB,MAAM;GAEnD;AACD,OAAI,WAAW,MACb,UAAS,MAAM;AACb,YAAQ;AACR,YAAQ;GACT,EAAC;OAEF,SAAQ;EAEX;EACD,SAAS,UAAU,QAAQ,eAAe;AACxC,OAAI,MAAM,aACR,cAAa,QAAQ;AAEvB,QAAK,MAAM,MACT,QAAO,OAAO;OAEd,OAAM,MAAM,UAAU,QAAQ,iBAAiB,CAAE,EAAC;EAErD;EACD,SAAS,UAAU;AACjB,OAAI,MAAM,OAAO;AACf,UAAM,MAAM,SAAS;AACrB,UAAM,aAAa;GACpB;EACF;EACD,IAAI,gBAAgB;AACpB,QACE,cACA,CAAC,kBAAkB;AACjB,cAAW,kBAAkB,YAAY;AACvC,mBAAe;AACf,oBAAgB;GACjB;AACD,QAAK,cACH,iBAAgB,MACd,MAAM,MAAM,QACZ,CAAC,QAAQ,cAAc;AACrB,SAAK,OACH;AAEF,SAAK,MAAM,MACT,SAAQ;QAER,OAAM,MAAM,UAAU,QAAQ;KAG5B,UAAU,WAAW;KACrB,GAAG,kBAAkB;IACtB,EAAC;GAEL,GACD,EAAE,MAAM,KAAM,EACf;EAEJ,GACD,EACE,WAAW,KACZ,EACF;AACD,QACE,CAAC,WAAW,eAAgB,GAC5B,MAAM;AACJ,YAAS;AACT,WAAQ;EACT,GACD,EACE,MAAM,KACP,EACF;AACD,cAAY,MAAM;AAChB,OAAI,MAAM,SAAS,MAAM,MACvB,OAAM,MAAM,QAAQ,MAAM;EAE7B,EAAC;EACF,MAAM,YAAY,aAAa,MAAM;AACrC,aAAW,OAAO,SAAS,eAAe;AAC1C,gBAAc,OAAO,YAAY,KAAK;AACtC,YAAU,MAAM;AACd,WAAQ;EACT,EAAC;AACF,kBAAgB,MAAM;AACpB,OAAI,gBAAgB,KAAK,MACvB,MAAK,MAAM,YAAY;OAEvB,UAAS;EAEZ,EAAC;AACF,SAAO;GACL;GACA;GACA;GACA;GACA;GACA,GAAG;EACJ;CACF;CACD,SAAS;EACP,MAAM,QAAQ,OAAO;GAAE,OAAO,KAAK;GAAe,IAAI,KAAK;EAAiB,IAAG;GAAE,GAAG,KAAK;GAAe,GAAG,KAAK;EAAiB;AACjI,QAAM,MAAM;AACZ,QAAM,QAAQ,MAAM,QAAQ,CAAC,SAAU,EAAC,OAAO,MAAM,MAAM,GAAG;AAC9D,SAAO,EAAE,UAAU,MAAM;CAC1B;AACF,EAAC"}