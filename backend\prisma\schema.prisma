// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model HousingListing {
  id               String   @id @default(cuid())
  listingId        String   @unique
  source           String   @db.VarChar(20)
  title            String   @db.VarChar(255)
  link             String   @db.VarChar(512)
  community        String   @db.VarChar(100)
  district         String   @db.VarChar(50)
  totalPrice       Float    // 单位：万
  unitPrice        Float    // 单位：元/平米
  area             Float
  layout           String   @db.VarChar(50)
  floor            String?  @db.VarChar(50)
  orientation      String?  @db.VarChar(50)
  buildYear        Int?
  imageUrl         String?  @db.VarChar(512)
  firstScrapedAt   DateTime @default(now())
  lastScrapedAt    DateTime @updatedAt
  isActive         Boolean  @default(true)

  priceHistory PriceHistory[]
  subscriptions Subscription[]

  @@index([source])
  @@index([community])
  @@index([district])
  @@map("housing_listing")
}

model PriceHistory {
  id         String   @id @default(cuid())
  price      Float    // 总价
  scrapedAt  DateTime @default(now())
  listing    HousingListing @relation(fields: [listingId], references: [id])
  listingId  String

  @@map("price_history")
}

enum SubscriptionType {
  NEW_LISTING
  PRICE_CHANGE
}

model Subscription {
  id              String   @id @default(cuid())
  subscriptionType SubscriptionType
  keywords        String?  @db.VarChar(255) // For NEW_LISTING
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())

  // Relation to a specific listing for PRICE_CHANGE
  listing         HousingListing? @relation(fields: [listingId], references: [id])
  listingId       String?

  @@map("subscription")
}
