import { computed, createBaseVNode, createElementBlock, createStaticVNode, defineComponent, getCurrentInstance, inject, normalizeClass, normalizeStyle, openBlock } from "./vue.runtime.esm-bundler-B8Y37iQy.js";

//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/_utils/is.js
const opt = Object.prototype.toString;
function isArray(obj) {
	return opt.call(obj) === "[object Array]";
}
function isNull(obj) {
	return opt.call(obj) === "[object Null]";
}
function isBoolean(obj) {
	return opt.call(obj) === "[object Boolean]";
}
function isObject(obj) {
	return opt.call(obj) === "[object Object]";
}
const isPromise = (obj) => {
	return opt.call(obj) === "[object Promise]";
};
function isString(obj) {
	return opt.call(obj) === "[object String]";
}
function isNumber(obj) {
	return opt.call(obj) === "[object Number]" && obj === obj;
}
function isUndefined(obj) {
	return obj === void 0;
}
function isFunction(obj) {
	return typeof obj === "function";
}
function isEmptyObject(obj) {
	return isObject(obj) && Object.keys(obj).length === 0;
}
function isExist(obj) {
	return obj || obj === 0;
}
function isWindow(el) {
	return el === window;
}
const isComponentInstance = (value) => {
	return (value == null ? void 0 : value.$) !== void 0;
};
const isQuarter = (fromat) => {
	return /\[Q]Q/.test(fromat);
};
function isDayjs(time) {
	return isObject(time) && "$y" in time && "$M" in time && "$D" in time && "$d" in time && "$H" in time && "$m" in time && "$s" in time;
}

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/config-provider/context.js
const configProviderInjectionKey = Symbol("ArcoConfigProvider");

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/_utils/global-config.js
const COMPONENT_PREFIX = "A";
const CLASS_PREFIX = "arco";
const GLOBAL_CONFIG_NAME = "$arco";
const getComponentPrefix = (options) => {
	var _a;
	return (_a = options == null ? void 0 : options.componentPrefix) != null ? _a : COMPONENT_PREFIX;
};
const setGlobalConfig = (app, options) => {
	var _a;
	if (options && options.classPrefix) app.config.globalProperties[GLOBAL_CONFIG_NAME] = {
		...(_a = app.config.globalProperties[GLOBAL_CONFIG_NAME]) != null ? _a : {},
		classPrefix: options.classPrefix
	};
};
const getPrefixCls = (componentName) => {
	var _a, _b, _c;
	const instance = getCurrentInstance();
	const configProvider = inject(configProviderInjectionKey, void 0);
	const prefix = (_c = (_b = configProvider == null ? void 0 : configProvider.prefixCls) != null ? _b : (_a = instance == null ? void 0 : instance.appContext.config.globalProperties[GLOBAL_CONFIG_NAME]) == null ? void 0 : _a.classPrefix) != null ? _c : CLASS_PREFIX;
	if (componentName) return `${prefix}-${componentName}`;
	return prefix;
};

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/_virtual/plugin-vue_export-helper.js
var _export_sfc = (sfc, props) => {
	for (const [key, val] of props) sfc[key] = val;
	return sfc;
};

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-close/icon-close.js
const _sfc_main$58 = defineComponent({
	name: "IconClose",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-close`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$58 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$58(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142" }, null, -1)]), 14, _hoisted_1$58);
}
var _IconClose = /* @__PURE__ */ _export_sfc(_sfc_main$58, [["render", _sfc_render$58]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-close/index.js
const IconClose = Object.assign(_IconClose, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconClose.name, _IconClose);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-info-circle-fill/icon-info-circle-fill.js
const _sfc_main$57 = defineComponent({
	name: "IconInfoCircleFill",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-info-circle-fill`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$57 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$57(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		"fill-rule": "evenodd",
		"clip-rule": "evenodd",
		d: "M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm2-30a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2Zm0 17h1a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-6a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h1v-8a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v11Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$57);
}
var _IconInfoCircleFill = /* @__PURE__ */ _export_sfc(_sfc_main$57, [["render", _sfc_render$57]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-info-circle-fill/index.js
const IconInfoCircleFill = Object.assign(_IconInfoCircleFill, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconInfoCircleFill.name, _IconInfoCircleFill);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-check-circle-fill/icon-check-circle-fill.js
const _sfc_main$56 = defineComponent({
	name: "IconCheckCircleFill",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-check-circle-fill`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$56 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$56(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		"fill-rule": "evenodd",
		"clip-rule": "evenodd",
		d: "M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm10.207-24.379a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0L22 26.172l-4.878-4.88a1 1 0 0 0-1.415 0l-1.414 1.415a1 1 0 0 0 0 1.414l7 7a1 1 0 0 0 1.414 0l11.5-11.5Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$56);
}
var _IconCheckCircleFill = /* @__PURE__ */ _export_sfc(_sfc_main$56, [["render", _sfc_render$56]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-check-circle-fill/index.js
const IconCheckCircleFill = Object.assign(_IconCheckCircleFill, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconCheckCircleFill.name, _IconCheckCircleFill);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-exclamation-circle-fill/icon-exclamation-circle-fill.js
const _sfc_main$55 = defineComponent({
	name: "IconExclamationCircleFill",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-exclamation-circle-fill`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$55 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$55(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		"fill-rule": "evenodd",
		"clip-rule": "evenodd",
		d: "M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm-2-11a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v2Zm4-18a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V15Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$55);
}
var _IconExclamationCircleFill = /* @__PURE__ */ _export_sfc(_sfc_main$55, [["render", _sfc_render$55]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-exclamation-circle-fill/index.js
const IconExclamationCircleFill = Object.assign(_IconExclamationCircleFill, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconExclamationCircleFill.name, _IconExclamationCircleFill);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-close-circle-fill/icon-close-circle-fill.js
const _sfc_main$54 = defineComponent({
	name: "IconCloseCircleFill",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-close-circle-fill`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$54 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$54(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		"fill-rule": "evenodd",
		"clip-rule": "evenodd",
		d: "M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm4.955-27.771-4.95 4.95-4.95-4.95a1 1 0 0 0-1.414 0l-1.414 1.414a1 1 0 0 0 0 1.414l4.95 4.95-4.95 4.95a1 1 0 0 0 0 1.414l1.414 1.414a1 1 0 0 0 1.414 0l4.95-4.95 4.95 4.95a1 1 0 0 0 1.414 0l1.414-1.414a1 1 0 0 0 0-1.414l-4.95-4.95 4.95-4.95a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$54);
}
var _IconCloseCircleFill = /* @__PURE__ */ _export_sfc(_sfc_main$54, [["render", _sfc_render$54]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-close-circle-fill/index.js
const IconCloseCircleFill = Object.assign(_IconCloseCircleFill, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconCloseCircleFill.name, _IconCloseCircleFill);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-loading/icon-loading.js
const _sfc_main$53 = defineComponent({
	name: "IconLoading",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-loading`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$53 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$53(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M42 24c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6" }, null, -1)]), 14, _hoisted_1$53);
}
var _IconLoading = /* @__PURE__ */ _export_sfc(_sfc_main$53, [["render", _sfc_render$53]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-loading/index.js
const IconLoading = Object.assign(_IconLoading, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconLoading.name, _IconLoading);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-search/icon-search.js
const _sfc_main$52 = defineComponent({
	name: "IconSearch",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-search`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$52 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$52(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M33.072 33.071c6.248-6.248 6.248-16.379 0-22.627-6.249-6.249-16.38-6.249-22.628 0-6.248 6.248-6.248 16.379 0 22.627 6.248 6.248 16.38 6.248 22.628 0Zm0 0 8.485 8.485" }, null, -1)]), 14, _hoisted_1$52);
}
var _IconSearch = /* @__PURE__ */ _export_sfc(_sfc_main$52, [["render", _sfc_render$52]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-search/index.js
const IconSearch = Object.assign(_IconSearch, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconSearch.name, _IconSearch);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-eye/icon-eye.js
const _sfc_main$51 = defineComponent({
	name: "IconEye",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-eye`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$51 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$51(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		"clip-rule": "evenodd",
		d: "M24 37c6.627 0 12.627-4.333 18-13-5.373-8.667-11.373-13-18-13-6.627 0-12.627 4.333-18 13 5.373 8.667 11.373 13 18 13Z"
	}, null, -1), createBaseVNode("path", { d: "M29 24a5 5 0 1 1-10 0 5 5 0 0 1 10 0Z" }, null, -1)]), 14, _hoisted_1$51);
}
var _IconEye = /* @__PURE__ */ _export_sfc(_sfc_main$51, [["render", _sfc_render$51]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-eye/index.js
const IconEye = Object.assign(_IconEye, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconEye.name, _IconEye);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-eye-invisible/icon-eye-invisible.js
const _sfc_main$50 = defineComponent({
	name: "IconEyeInvisible",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-eye-invisible`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$50 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$50(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M14 14.5c-2.69 2-5.415 5.33-8 9.5 5.373 8.667 11.373 13 18 13 3.325 0 6.491-1.09 9.5-3.271M17.463 12.5C19 11 21.75 11 24 11c6.627 0 12.627 4.333 18 13-1.766 2.848-3.599 5.228-5.5 7.14" }, null, -1), createBaseVNode("path", { d: "M29 24a5 5 0 1 1-10 0 5 5 0 0 1 10 0ZM6.852 7.103l34.294 34.294" }, null, -1)]), 14, _hoisted_1$50);
}
var _IconEyeInvisible = /* @__PURE__ */ _export_sfc(_sfc_main$50, [["render", _sfc_render$50]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-eye-invisible/index.js
const IconEyeInvisible = Object.assign(_IconEyeInvisible, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconEyeInvisible.name, _IconEyeInvisible);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-empty/icon-empty.js
const _sfc_main$49 = defineComponent({
	name: "IconEmpty",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-empty`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$49 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$49(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M24 5v6m7 1 4-4m-18 4-4-4m28.5 22H28s-1 3-4 3-4-3-4-3H6.5M40 41H8a2 2 0 0 1-2-2v-8.46a2 2 0 0 1 .272-1.007l6.15-10.54A2 2 0 0 1 14.148 18H33.85a2 2 0 0 1 1.728.992l6.149 10.541A2 2 0 0 1 42 30.541V39a2 2 0 0 1-2 2Z" }, null, -1)]), 14, _hoisted_1$49);
}
var _IconEmpty = /* @__PURE__ */ _export_sfc(_sfc_main$49, [["render", _sfc_render$49]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-empty/index.js
const IconEmpty = Object.assign(_IconEmpty, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconEmpty.name, _IconEmpty);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-image-close/icon-image-close.js
const _sfc_main$48 = defineComponent({
	name: "IconImageClose",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-image-close`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$48 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$48(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createStaticVNode("<path d=\"M41 26V9a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2v30a2 2 0 0 0 2 2h17\"></path><path d=\"m24 33 9-8.5V27s-2 1-3.5 2.5C27.841 31.159 27 33 27 33h-3Zm0 0-3.5-4.5L17 33h7Z\"></path><path d=\"M20.5 28.5 17 33h7l-3.5-4.5ZM33 24.5 24 33h3s.841-1.841 2.5-3.5C31 28 33 27 33 27v-2.5Z\" fill=\"currentColor\" stroke=\"none\"></path><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M46 38a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-4.95-4.782 1.74 1.74-3.045 3.046 3.046 3.046-1.74 1.74-3.047-3.045-3.046 3.046-1.74-1.74 3.046-3.047-3.046-3.046 1.74-1.74 3.046 3.046 3.046-3.046Z\" fill=\"currentColor\" stroke=\"none\"></path><path d=\"M17 15h-2v2h2v-2Z\"></path>", 5)]), 14, _hoisted_1$48);
}
var _IconImageClose = /* @__PURE__ */ _export_sfc(_sfc_main$48, [["render", _sfc_render$48]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-image-close/index.js
const IconImageClose = Object.assign(_IconImageClose, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconImageClose.name, _IconImageClose);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-to-top/icon-to-top.js
const _sfc_main$47 = defineComponent({
	name: "IconToTop",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-to-top`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$47 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$47(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M43 7H5M24 20v23M24 13.96 30.453 21H17.546L24 13.96Zm.736-.804Z" }, null, -1), createBaseVNode("path", {
		d: "m24 14-6 7h12l-6-7Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$47);
}
var _IconToTop = /* @__PURE__ */ _export_sfc(_sfc_main$47, [["render", _sfc_render$47]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-to-top/index.js
const IconToTop = Object.assign(_IconToTop, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconToTop.name, _IconToTop);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-more/icon-more.js
const _sfc_main$46 = defineComponent({
	name: "IconMore",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-more`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$46 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$46(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		d: "M38 25v-2h2v2h-2ZM23 25v-2h2v2h-2ZM8 25v-2h2v2H8Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1), createBaseVNode("path", { d: "M38 25v-2h2v2h-2ZM23 25v-2h2v2h-2ZM8 25v-2h2v2H8Z" }, null, -1)]), 14, _hoisted_1$46);
}
var _IconMore = /* @__PURE__ */ _export_sfc(_sfc_main$46, [["render", _sfc_render$46]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-more/index.js
const IconMore = Object.assign(_IconMore, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconMore.name, _IconMore);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-down/icon-down.js
const _sfc_main$45 = defineComponent({
	name: "IconDown",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-down`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$45 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$45(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M39.6 17.443 24.043 33 8.487 17.443" }, null, -1)]), 14, _hoisted_1$45);
}
var _IconDown = /* @__PURE__ */ _export_sfc(_sfc_main$45, [["render", _sfc_render$45]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-down/index.js
const IconDown = Object.assign(_IconDown, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconDown.name, _IconDown);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-oblique-line/icon-oblique-line.js
const _sfc_main$44 = defineComponent({
	name: "IconObliqueLine",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-oblique-line`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$44 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$44(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M29.506 6.502 18.493 41.498" }, null, -1)]), 14, _hoisted_1$44);
}
var _IconObliqueLine = /* @__PURE__ */ _export_sfc(_sfc_main$44, [["render", _sfc_render$44]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-oblique-line/index.js
const IconObliqueLine = Object.assign(_IconObliqueLine, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconObliqueLine.name, _IconObliqueLine);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-right/icon-right.js
const _sfc_main$43 = defineComponent({
	name: "IconRight",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-right`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$43 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$43(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "m16 39.513 15.556-15.557L16 8.4" }, null, -1)]), 14, _hoisted_1$43);
}
var _IconRight = /* @__PURE__ */ _export_sfc(_sfc_main$43, [["render", _sfc_render$43]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-right/index.js
const IconRight = Object.assign(_IconRight, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconRight.name, _IconRight);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-left/icon-left.js
const _sfc_main$42 = defineComponent({
	name: "IconLeft",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-left`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$42 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$42(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M32 8.4 16.444 23.956 32 39.513" }, null, -1)]), 14, _hoisted_1$42);
}
var _IconLeft = /* @__PURE__ */ _export_sfc(_sfc_main$42, [["render", _sfc_render$42]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-left/index.js
const IconLeft = Object.assign(_IconLeft, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconLeft.name, _IconLeft);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-up/icon-up.js
const _sfc_main$41 = defineComponent({
	name: "IconUp",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-up`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$41 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$41(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M39.6 30.557 24.043 15 8.487 30.557" }, null, -1)]), 14, _hoisted_1$41);
}
var _IconUp = /* @__PURE__ */ _export_sfc(_sfc_main$41, [["render", _sfc_render$41]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-up/index.js
const IconUp = Object.assign(_IconUp, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconUp.name, _IconUp);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-caret-right/icon-caret-right.js
const _sfc_main$40 = defineComponent({
	name: "IconCaretRight",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-caret-right`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$40 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$40(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		d: "M34.829 23.063c.6.48.6 1.394 0 1.874L17.949 38.44c-.785.629-1.949.07-1.949-.937V10.497c0-1.007 1.164-1.566 1.95-.937l16.879 13.503Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$40);
}
var _IconCaretRight = /* @__PURE__ */ _export_sfc(_sfc_main$40, [["render", _sfc_render$40]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-caret-right/index.js
const IconCaretRight = Object.assign(_IconCaretRight, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconCaretRight.name, _IconCaretRight);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-caret-left/icon-caret-left.js
const _sfc_main$39 = defineComponent({
	name: "IconCaretLeft",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-caret-left`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$39 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$39(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		d: "M13.171 24.937a1.2 1.2 0 0 1 0-1.874L30.051 9.56c.785-.629 1.949-.07 1.949.937v27.006c0 1.006-1.164 1.566-1.95.937L13.171 24.937Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$39);
}
var _IconCaretLeft = /* @__PURE__ */ _export_sfc(_sfc_main$39, [["render", _sfc_render$39]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-caret-left/index.js
const IconCaretLeft = Object.assign(_IconCaretLeft, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconCaretLeft.name, _IconCaretLeft);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-plus/icon-plus.js
const _sfc_main$38 = defineComponent({
	name: "IconPlus",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-plus`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$38 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$38(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M5 24h38M24 5v38" }, null, -1)]), 14, _hoisted_1$38);
}
var _IconPlus = /* @__PURE__ */ _export_sfc(_sfc_main$38, [["render", _sfc_render$38]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-plus/index.js
const IconPlus = Object.assign(_IconPlus, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconPlus.name, _IconPlus);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-minus/icon-minus.js
const _sfc_main$37 = defineComponent({
	name: "IconMinus",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-minus`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$37 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$37(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M5 24h38" }, null, -1)]), 14, _hoisted_1$37);
}
var _IconMinus = /* @__PURE__ */ _export_sfc(_sfc_main$37, [["render", _sfc_render$37]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-minus/index.js
const IconMinus = Object.assign(_IconMinus, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconMinus.name, _IconMinus);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-double-left/icon-double-left.js
const _sfc_main$36 = defineComponent({
	name: "IconDoubleLeft",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-double-left`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$36 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$36(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M36.857 9.9 22.715 24.042l14.142 14.142M25.544 9.9 11.402 24.042l14.142 14.142" }, null, -1)]), 14, _hoisted_1$36);
}
var _IconDoubleLeft = /* @__PURE__ */ _export_sfc(_sfc_main$36, [["render", _sfc_render$36]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-double-left/index.js
const IconDoubleLeft = Object.assign(_IconDoubleLeft, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconDoubleLeft.name, _IconDoubleLeft);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-double-right/icon-double-right.js
const _sfc_main$35 = defineComponent({
	name: "IconDoubleRight",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-double-right`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$35 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$35(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "m11.143 38.1 14.142-14.142L11.143 9.816M22.456 38.1l14.142-14.142L22.456 9.816" }, null, -1)]), 14, _hoisted_1$35);
}
var _IconDoubleRight = /* @__PURE__ */ _export_sfc(_sfc_main$35, [["render", _sfc_render$35]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-double-right/index.js
const IconDoubleRight = Object.assign(_IconDoubleRight, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconDoubleRight.name, _IconDoubleRight);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-calendar/icon-calendar.js
const _sfc_main$34 = defineComponent({
	name: "IconCalendar",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-calendar`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$34 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$34(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z" }, null, -1)]), 14, _hoisted_1$34);
}
var _IconCalendar = /* @__PURE__ */ _export_sfc(_sfc_main$34, [["render", _sfc_render$34]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-calendar/index.js
const IconCalendar = Object.assign(_IconCalendar, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconCalendar.name, _IconCalendar);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-clock-circle/icon-clock-circle.js
const _sfc_main$33 = defineComponent({
	name: "IconClockCircle",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-clock-circle`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$33 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$33(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z" }, null, -1)]), 14, _hoisted_1$33);
}
var _IconClockCircle = /* @__PURE__ */ _export_sfc(_sfc_main$33, [["render", _sfc_render$33]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-clock-circle/index.js
const IconClockCircle = Object.assign(_IconClockCircle, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconClockCircle.name, _IconClockCircle);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-link/icon-link.js
const _sfc_main$32 = defineComponent({
	name: "IconLink",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-link`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$32 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$32(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "m14.1 25.414-4.95 4.95a6 6 0 0 0 8.486 8.485l8.485-8.485a6 6 0 0 0 0-8.485m7.779.707 4.95-4.95a6 6 0 1 0-8.486-8.485l-8.485 8.485a6 6 0 0 0 0 8.485" }, null, -1)]), 14, _hoisted_1$32);
}
var _IconLink = /* @__PURE__ */ _export_sfc(_sfc_main$32, [["render", _sfc_render$32]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-link/index.js
const IconLink = Object.assign(_IconLink, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconLink.name, _IconLink);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-question-circle/icon-question-circle.js
const _sfc_main$31 = defineComponent({
	name: "IconQuestionCircle",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-question-circle`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$31 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$31(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M42 24c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z" }, null, -1), createBaseVNode("path", { d: "M24.006 31v4.008m0-6.008L24 28c0-3 3-4 4.78-6.402C30.558 19.195 28.288 15 23.987 15c-4.014 0-5.382 2.548-5.388 4.514v.465" }, null, -1)]), 14, _hoisted_1$31);
}
var _IconQuestionCircle = /* @__PURE__ */ _export_sfc(_sfc_main$31, [["render", _sfc_render$31]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-question-circle/index.js
const IconQuestionCircle = Object.assign(_IconQuestionCircle, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconQuestionCircle.name, _IconQuestionCircle);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-zoom-out/icon-zoom-out.js
const _sfc_main$30 = defineComponent({
	name: "IconZoomOut",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-zoom-out`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$30 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$30(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M32.607 32.607A14.953 14.953 0 0 0 37 22c0-8.284-6.716-15-15-15-8.284 0-15 6.716-15 15 0 8.284 6.716 15 15 15 4.142 0 7.892-1.679 10.607-4.393Zm0 0L41.5 41.5M29 22H15" }, null, -1)]), 14, _hoisted_1$30);
}
var _IconZoomOut = /* @__PURE__ */ _export_sfc(_sfc_main$30, [["render", _sfc_render$30]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-zoom-out/index.js
const IconZoomOut = Object.assign(_IconZoomOut, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconZoomOut.name, _IconZoomOut);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-zoom-in/icon-zoom-in.js
const _sfc_main$29 = defineComponent({
	name: "IconZoomIn",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-zoom-in`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$29 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$29(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M32.607 32.607A14.953 14.953 0 0 0 37 22c0-8.284-6.716-15-15-15-8.284 0-15 6.716-15 15 0 8.284 6.716 15 15 15 4.142 0 7.892-1.679 10.607-4.393Zm0 0L41.5 41.5M29 22H15m7 7V15" }, null, -1)]), 14, _hoisted_1$29);
}
var _IconZoomIn = /* @__PURE__ */ _export_sfc(_sfc_main$29, [["render", _sfc_render$29]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-zoom-in/index.js
const IconZoomIn = Object.assign(_IconZoomIn, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconZoomIn.name, _IconZoomIn);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-fullscreen/icon-fullscreen.js
const _sfc_main$28 = defineComponent({
	name: "IconFullscreen",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-fullscreen`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$28 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$28(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M42 17V9a1 1 0 0 0-1-1h-8M6 17V9a1 1 0 0 1 1-1h8m27 23v8a1 1 0 0 1-1 1h-8M6 31v8a1 1 0 0 0 1 1h8" }, null, -1)]), 14, _hoisted_1$28);
}
var _IconFullscreen = /* @__PURE__ */ _export_sfc(_sfc_main$28, [["render", _sfc_render$28]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-fullscreen/index.js
const IconFullscreen = Object.assign(_IconFullscreen, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconFullscreen.name, _IconFullscreen);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-rotate-left/icon-rotate-left.js
const _sfc_main$27 = defineComponent({
	name: "IconRotateLeft",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-rotate-left`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$27 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$27(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M10 22a1 1 0 0 1 1-1h20a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H11a1 1 0 0 1-1-1V22ZM23 11h11a6 6 0 0 1 6 6v6M22.5 12.893 19.587 11 22.5 9.107v3.786Z" }, null, -1)]), 14, _hoisted_1$27);
}
var _IconRotateLeft = /* @__PURE__ */ _export_sfc(_sfc_main$27, [["render", _sfc_render$27]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-rotate-left/index.js
const IconRotateLeft = Object.assign(_IconRotateLeft, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconRotateLeft.name, _IconRotateLeft);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-rotate-right/icon-rotate-right.js
const _sfc_main$26 = defineComponent({
	name: "IconRotateRight",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-rotate-right`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$26 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$26(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M38 22a1 1 0 0 0-1-1H17a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h20a1 1 0 0 0 1-1V22ZM25 11H14a6 6 0 0 0-6 6v6M25.5 12.893 28.413 11 25.5 9.107v3.786Z" }, null, -1)]), 14, _hoisted_1$26);
}
var _IconRotateRight = /* @__PURE__ */ _export_sfc(_sfc_main$26, [["render", _sfc_render$26]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-rotate-right/index.js
const IconRotateRight = Object.assign(_IconRotateRight, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconRotateRight.name, _IconRotateRight);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-original-size/icon-original-size.js
const _sfc_main$25 = defineComponent({
	name: "IconOriginalSize",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-original-size`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$25 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$25(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [
		createBaseVNode("path", { d: "m5.5 11.5 5-2.5h1v32M34 11.5 39 9h1v32" }, null, -1),
		createBaseVNode("path", {
			d: "M24 17h1v1h-1v-1ZM24 30h1v1h-1v-1Z",
			fill: "currentColor",
			stroke: "none"
		}, null, -1),
		createBaseVNode("path", { d: "M24 17h1v1h-1v-1ZM24 30h1v1h-1v-1Z" }, null, -1)
	]), 14, _hoisted_1$25);
}
var _IconOriginalSize = /* @__PURE__ */ _export_sfc(_sfc_main$25, [["render", _sfc_render$25]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-original-size/index.js
const IconOriginalSize = Object.assign(_IconOriginalSize, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconOriginalSize.name, _IconOriginalSize);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-drag-dot/icon-drag-dot.js
const _sfc_main$24 = defineComponent({
	name: "IconDragDot",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-drag-dot`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$24 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$24(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		d: "M40 17v2h-2v-2h2ZM25 17v2h-2v-2h2ZM10 17v2H8v-2h2ZM40 29v2h-2v-2h2ZM25 29v2h-2v-2h2ZM10 29v2H8v-2h2Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1), createBaseVNode("path", { d: "M40 17v2h-2v-2h2ZM25 17v2h-2v-2h2ZM10 17v2H8v-2h2ZM40 29v2h-2v-2h2ZM25 29v2h-2v-2h2ZM10 29v2H8v-2h2Z" }, null, -1)]), 14, _hoisted_1$24);
}
var _IconDragDot = /* @__PURE__ */ _export_sfc(_sfc_main$24, [["render", _sfc_render$24]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-drag-dot/index.js
const IconDragDot = Object.assign(_IconDragDot, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconDragDot.name, _IconDragDot);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-drag-dot-vertical/icon-drag-dot-vertical.js
const _sfc_main$23 = defineComponent({
	name: "IconDragDotVertical",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-drag-dot-vertical`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$23 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$23(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		d: "M17 8h2v2h-2V8ZM17 23h2v2h-2v-2ZM17 38h2v2h-2v-2ZM29 8h2v2h-2V8ZM29 23h2v2h-2v-2ZM29 38h2v2h-2v-2Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1), createBaseVNode("path", { d: "M17 8h2v2h-2V8ZM17 23h2v2h-2v-2ZM17 38h2v2h-2v-2ZM29 8h2v2h-2V8ZM29 23h2v2h-2v-2ZM29 38h2v2h-2v-2Z" }, null, -1)]), 14, _hoisted_1$23);
}
var _IconDragDotVertical = /* @__PURE__ */ _export_sfc(_sfc_main$23, [["render", _sfc_render$23]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-drag-dot-vertical/index.js
const IconDragDotVertical = Object.assign(_IconDragDotVertical, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconDragDotVertical.name, _IconDragDotVertical);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-menu-fold/icon-menu-fold.js
const _sfc_main$22 = defineComponent({
	name: "IconMenuFold",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-menu-fold`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$22 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$22(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M42 11H6M42 24H22M42 37H6M13.66 26.912l-4.82-3.118 4.82-3.118v6.236Z" }, null, -1)]), 14, _hoisted_1$22);
}
var _IconMenuFold = /* @__PURE__ */ _export_sfc(_sfc_main$22, [["render", _sfc_render$22]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-menu-fold/index.js
const IconMenuFold = Object.assign(_IconMenuFold, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconMenuFold.name, _IconMenuFold);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-menu-unfold/icon-menu-unfold.js
const _sfc_main$21 = defineComponent({
	name: "IconMenuUnfold",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-menu-unfold`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$21 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$21(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M6 11h36M22 24h20M6 37h36M8 20.882 12.819 24 8 27.118v-6.236Z" }, null, -1)]), 14, _hoisted_1$21);
}
var _IconMenuUnfold = /* @__PURE__ */ _export_sfc(_sfc_main$21, [["render", _sfc_render$21]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-menu-unfold/index.js
const IconMenuUnfold = Object.assign(_IconMenuUnfold, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconMenuUnfold.name, _IconMenuUnfold);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-exclamation/icon-exclamation.js
const _sfc_main$20 = defineComponent({
	name: "IconExclamation",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-exclamation`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$20 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$20(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [
		createBaseVNode("path", { d: "M23 9h2v21h-2z" }, null, -1),
		createBaseVNode("path", {
			fill: "currentColor",
			stroke: "none",
			d: "M23 9h2v21h-2z"
		}, null, -1),
		createBaseVNode("path", { d: "M23 37h2v2h-2z" }, null, -1),
		createBaseVNode("path", {
			fill: "currentColor",
			stroke: "none",
			d: "M23 37h2v2h-2z"
		}, null, -1)
	]), 14, _hoisted_1$20);
}
var _IconExclamation = /* @__PURE__ */ _export_sfc(_sfc_main$20, [["render", _sfc_render$20]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-exclamation/index.js
const IconExclamation = Object.assign(_IconExclamation, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconExclamation.name, _IconExclamation);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-check/icon-check.js
const _sfc_main$19 = defineComponent({
	name: "IconCheck",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-check`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$19 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$19(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M41.678 11.05 19.05 33.678 6.322 20.95" }, null, -1)]), 14, _hoisted_1$19);
}
var _IconCheck = /* @__PURE__ */ _export_sfc(_sfc_main$19, [["render", _sfc_render$19]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-check/index.js
const IconCheck = Object.assign(_IconCheck, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconCheck.name, _IconCheck);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-star-fill/icon-star-fill.js
const _sfc_main$18 = defineComponent({
	name: "IconStarFill",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-star-fill`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$18 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$18(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		d: "M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$18);
}
var _IconStarFill = /* @__PURE__ */ _export_sfc(_sfc_main$18, [["render", _sfc_render$18]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-star-fill/index.js
const IconStarFill = Object.assign(_IconStarFill, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconStarFill.name, _IconStarFill);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-face-meh-fill/icon-face-meh-fill.js
const _sfc_main$17 = defineComponent({
	name: "IconFaceMehFill",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-face-meh-fill`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$17 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$17(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		"fill-rule": "evenodd",
		"clip-rule": "evenodd",
		d: "M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm7.321-26.873a2.625 2.625 0 1 1 0 5.25 2.625 2.625 0 0 1 0-5.25Zm-14.646 0a2.625 2.625 0 1 1 0 5.25 2.625 2.625 0 0 1 0-5.25ZM15.999 30a2 2 0 0 1 2-2h12a2 2 0 1 1 0 4H18a2 2 0 0 1-2-2Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$17);
}
var _IconFaceMehFill = /* @__PURE__ */ _export_sfc(_sfc_main$17, [["render", _sfc_render$17]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-face-meh-fill/index.js
const IconFaceMehFill = Object.assign(_IconFaceMehFill, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconFaceMehFill.name, _IconFaceMehFill);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-face-smile-fill/icon-face-smile-fill.js
const _sfc_main$16 = defineComponent({
	name: "IconFaceSmileFill",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-face-smile-fill`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$16 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$16(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		"fill-rule": "evenodd",
		"clip-rule": "evenodd",
		d: "M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm7.321-26.873a2.625 2.625 0 1 1 0 5.25 2.625 2.625 0 0 1 0-5.25Zm-14.646 0a2.625 2.625 0 1 1 0 5.25 2.625 2.625 0 0 1 0-5.25Zm-.355 9.953a1.91 1.91 0 0 1 2.694.177 6.66 6.66 0 0 0 5.026 2.279c1.918 0 3.7-.81 4.961-2.206a1.91 1.91 0 0 1 2.834 2.558 10.476 10.476 0 0 1-7.795 3.466 10.477 10.477 0 0 1-7.897-3.58 1.91 1.91 0 0 1 .177-2.694Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$16);
}
var _IconFaceSmileFill = /* @__PURE__ */ _export_sfc(_sfc_main$16, [["render", _sfc_render$16]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-face-smile-fill/index.js
const IconFaceSmileFill = Object.assign(_IconFaceSmileFill, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconFaceSmileFill.name, _IconFaceSmileFill);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-face-frown-fill/icon-face-frown-fill.js
const _sfc_main$15 = defineComponent({
	name: "IconFaceFrownFill",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-face-frown-fill`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$15 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$15(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		"fill-rule": "evenodd",
		"clip-rule": "evenodd",
		d: "M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm7.322-26.873a2.625 2.625 0 1 1 0 5.25 2.625 2.625 0 0 1 0-5.25Zm-14.646 0a2.625 2.625 0 1 1 0 5.25 2.625 2.625 0 0 1 0-5.25ZM31.68 32.88a1.91 1.91 0 0 1-2.694-.176 6.66 6.66 0 0 0-5.026-2.28c-1.918 0-3.701.81-4.962 2.207a1.91 1.91 0 0 1-2.834-2.559 10.476 10.476 0 0 1 7.796-3.465c3.063 0 5.916 1.321 7.896 3.58a1.909 1.909 0 0 1-.176 2.693Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$15);
}
var _IconFaceFrownFill = /* @__PURE__ */ _export_sfc(_sfc_main$15, [["render", _sfc_render$15]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-face-frown-fill/index.js
const IconFaceFrownFill = Object.assign(_IconFaceFrownFill, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconFaceFrownFill.name, _IconFaceFrownFill);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-info/icon-info.js
const _sfc_main$14 = defineComponent({
	name: "IconInfo",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-info`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$14 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$14(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [
		createBaseVNode("path", { d: "M25 39h-2V18h2z" }, null, -1),
		createBaseVNode("path", {
			fill: "currentColor",
			stroke: "none",
			d: "M25 39h-2V18h2z"
		}, null, -1),
		createBaseVNode("path", { d: "M25 11h-2V9h2z" }, null, -1),
		createBaseVNode("path", {
			fill: "currentColor",
			stroke: "none",
			d: "M25 11h-2V9h2z"
		}, null, -1)
	]), 14, _hoisted_1$14);
}
var _IconInfo = /* @__PURE__ */ _export_sfc(_sfc_main$14, [["render", _sfc_render$14]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-info/index.js
const IconInfo = Object.assign(_IconInfo, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconInfo.name, _IconInfo);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-caret-down/icon-caret-down.js
const _sfc_main$13 = defineComponent({
	name: "IconCaretDown",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-caret-down`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$13 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$13(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		d: "M24.938 34.829a1.2 1.2 0 0 1-1.875 0L9.56 17.949c-.628-.785-.069-1.949.937-1.949h27.007c1.006 0 1.565 1.164.937 1.95L24.937 34.829Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$13);
}
var _IconCaretDown = /* @__PURE__ */ _export_sfc(_sfc_main$13, [["render", _sfc_render$13]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-caret-down/index.js
const IconCaretDown = Object.assign(_IconCaretDown, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconCaretDown.name, _IconCaretDown);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-caret-up/icon-caret-up.js
const _sfc_main$12 = defineComponent({
	name: "IconCaretUp",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-caret-up`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$12 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$12(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		d: "M23.063 13.171a1.2 1.2 0 0 1 1.875 0l13.503 16.88c.628.785.069 1.949-.937 1.949H10.497c-1.006 0-1.565-1.164-.937-1.95l13.503-16.879Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$12);
}
var _IconCaretUp = /* @__PURE__ */ _export_sfc(_sfc_main$12, [["render", _sfc_render$12]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-caret-up/index.js
const IconCaretUp = Object.assign(_IconCaretUp, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconCaretUp.name, _IconCaretUp);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-filter/icon-filter.js
const _sfc_main$11 = defineComponent({
	name: "IconFilter",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-filter`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$11 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$11(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M30 42V22.549a1 1 0 0 1 .463-.844l10.074-6.41A1 1 0 0 0 41 14.45V8a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v6.451a1 1 0 0 0 .463.844l10.074 6.41a1 1 0 0 1 .463.844V37" }, null, -1)]), 14, _hoisted_1$11);
}
var _IconFilter = /* @__PURE__ */ _export_sfc(_sfc_main$11, [["render", _sfc_render$11]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-filter/index.js
const IconFilter = Object.assign(_IconFilter, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconFilter.name, _IconFilter);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-delete/icon-delete.js
const _sfc_main$10 = defineComponent({
	name: "IconDelete",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-delete`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$10 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$10(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M5 11h5.5m0 0v29a1 1 0 0 0 1 1h25a1 1 0 0 0 1-1V11m-27 0H16m21.5 0H43m-5.5 0H32m-16 0V7h16v4m-16 0h16M20 18v15m8-15v15" }, null, -1)]), 14, _hoisted_1$10);
}
var _IconDelete = /* @__PURE__ */ _export_sfc(_sfc_main$10, [["render", _sfc_render$10]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-delete/index.js
const IconDelete = Object.assign(_IconDelete, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconDelete.name, _IconDelete);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-file/icon-file.js
const _sfc_main$9 = defineComponent({
	name: "IconFile",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-file`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$9 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$9(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M16 21h16m-16 8h10m11 13H11a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h21l7 7v27a2 2 0 0 1-2 2Z" }, null, -1)]), 14, _hoisted_1$9);
}
var _IconFile = /* @__PURE__ */ _export_sfc(_sfc_main$9, [["render", _sfc_render$9]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-file/index.js
const IconFile = Object.assign(_IconFile, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconFile.name, _IconFile);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-copy/icon-copy.js
const _sfc_main$8 = defineComponent({
	name: "IconCopy",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-copy`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$8 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$8(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M20 6h18a2 2 0 0 1 2 2v22M8 16v24c0 1.105.891 2 1.996 2h20.007A1.99 1.99 0 0 0 32 40.008V15.997A1.997 1.997 0 0 0 30 14H10a2 2 0 0 0-2 2Z" }, null, -1)]), 14, _hoisted_1$8);
}
var _IconCopy = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["render", _sfc_render$8]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-copy/index.js
const IconCopy = Object.assign(_IconCopy, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconCopy.name, _IconCopy);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-edit/icon-edit.js
const _sfc_main$7 = defineComponent({
	name: "IconEdit",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-edit`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$7 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$7(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "m30.48 19.038 5.733-5.734a1 1 0 0 0 0-1.414l-5.586-5.586a1 1 0 0 0-1.414 0l-5.734 5.734m7 7L15.763 33.754a1 1 0 0 1-.59.286l-6.048.708a1 1 0 0 1-1.113-1.069l.477-6.31a1 1 0 0 1 .29-.631l14.7-14.7m7 7-7-7M6 42h36" }, null, -1)]), 14, _hoisted_1$7);
}
var _IconEdit = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["render", _sfc_render$7]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-edit/index.js
const IconEdit = Object.assign(_IconEdit, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconEdit.name, _IconEdit);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-upload/icon-upload.js
const _sfc_main$6 = defineComponent({
	name: "IconUpload",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-upload`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$6 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$6(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M14.93 17.071 24.001 8l9.071 9.071m-9.07 16.071v-25M40 35v6H8v-6" }, null, -1)]), 14, _hoisted_1$6);
}
var _IconUpload = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["render", _sfc_render$6]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-upload/index.js
const IconUpload = Object.assign(_IconUpload, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconUpload.name, _IconUpload);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-pause/icon-pause.js
const _sfc_main$5 = defineComponent({
	name: "IconPause",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-pause`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$5 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$5(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M14 12h4v24h-4zM30 12h4v24h-4z" }, null, -1), createBaseVNode("path", {
		fill: "currentColor",
		stroke: "none",
		d: "M14 12h4v24h-4zM30 12h4v24h-4z"
	}, null, -1)]), 14, _hoisted_1$5);
}
var _IconPause = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["render", _sfc_render$5]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-pause/index.js
const IconPause = Object.assign(_IconPause, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconPause.name, _IconPause);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-play-arrow-fill/icon-play-arrow-fill.js
const _sfc_main$4 = defineComponent({
	name: "IconPlayArrowFill",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-play-arrow-fill`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$4 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$4(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", {
		d: "M17.533 10.974a1 1 0 0 0-1.537.844v24.356a1 1 0 0 0 1.537.844L36.67 24.84a1 1 0 0 0 0-1.688L17.533 10.974Z",
		fill: "currentColor",
		stroke: "none"
	}, null, -1)]), 14, _hoisted_1$4);
}
var _IconPlayArrowFill = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["render", _sfc_render$4]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-play-arrow-fill/index.js
const IconPlayArrowFill = Object.assign(_IconPlayArrowFill, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconPlayArrowFill.name, _IconPlayArrowFill);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-file-pdf/icon-file-pdf.js
const _sfc_main$3 = defineComponent({
	name: "IconFilePdf",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-file-pdf`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$3 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$3(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M11 42h26a2 2 0 0 0 2-2V13.828a2 2 0 0 0-.586-1.414l-5.828-5.828A2 2 0 0 0 31.172 6H11a2 2 0 0 0-2 2v32a2 2 0 0 0 2 2Z" }, null, -1), createBaseVNode("path", { d: "M22.305 21.028c.874 1.939 3.506 6.265 4.903 8.055 1.747 2.237 3.494 2.685 4.368 2.237.873-.447 1.21-4.548-7.425-2.685-7.523 1.623-7.424 3.58-6.988 4.476.728 1.193 2.522 2.627 5.678-6.266C25.699 18.79 24.489 17 23.277 17c-1.409 0-2.538.805-.972 4.028Z" }, null, -1)]), 14, _hoisted_1$3);
}
var _IconFilePdf = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render$3]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-file-pdf/index.js
const IconFilePdf = Object.assign(_IconFilePdf, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconFilePdf.name, _IconFilePdf);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-file-image/icon-file-image.js
const _sfc_main$2 = defineComponent({
	name: "IconFileImage",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-file-image`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$2 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "m26 33 5-6v6h-5Zm0 0-3-4-4 4h7Zm11 9H11a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h21l7 7v27a2 2 0 0 1-2 2ZM17 19h1v1h-1v-1Z" }, null, -1)]), 14, _hoisted_1$2);
}
var _IconFileImage = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$2]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-file-image/index.js
const IconFileImage = Object.assign(_IconFileImage, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconFileImage.name, _IconFileImage);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-file-video/icon-file-video.js
const _sfc_main$1 = defineComponent({
	name: "IconFileVideo",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-file-video`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1$1 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [createBaseVNode("path", { d: "M37 42H11a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h21l7 7v27a2 2 0 0 1-2 2Z" }, null, -1), createBaseVNode("path", { d: "M22 27.796v-6l5 3-5 3Z" }, null, -1)]), 14, _hoisted_1$1);
}
var _IconFileVideo = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render$1]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-file-video/index.js
const IconFileVideo = Object.assign(_IconFileVideo, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconFileVideo.name, _IconFileVideo);
} });

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-file-audio/icon-file-audio.js
const _sfc_main = defineComponent({
	name: "IconFileAudio",
	props: {
		size: { type: [Number, String] },
		strokeWidth: {
			type: Number,
			default: 4
		},
		strokeLinecap: {
			type: String,
			default: "butt",
			validator: (value) => {
				return [
					"butt",
					"round",
					"square"
				].includes(value);
			}
		},
		strokeLinejoin: {
			type: String,
			default: "miter",
			validator: (value) => {
				return [
					"arcs",
					"bevel",
					"miter",
					"miter-clip",
					"round"
				].includes(value);
			}
		},
		rotate: Number,
		spin: Boolean
	},
	emits: { click: (ev) => true },
	setup(props, { emit }) {
		const prefixCls = getPrefixCls("icon");
		const cls = computed(() => [
			prefixCls,
			`${prefixCls}-file-audio`,
			{ [`${prefixCls}-spin`]: props.spin }
		]);
		const innerStyle = computed(() => {
			const styles = {};
			if (props.size) styles.fontSize = isNumber(props.size) ? `${props.size}px` : props.size;
			if (props.rotate) styles.transform = `rotate(${props.rotate}deg)`;
			return styles;
		});
		const onClick = (ev) => {
			emit("click", ev);
		};
		return {
			cls,
			innerStyle,
			onClick
		};
	}
});
const _hoisted_1 = [
	"stroke-width",
	"stroke-linecap",
	"stroke-linejoin"
];
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
	return openBlock(), createElementBlock("svg", {
		viewBox: "0 0 48 48",
		fill: "none",
		xmlns: "http://www.w3.org/2000/svg",
		stroke: "currentColor",
		class: normalizeClass(_ctx.cls),
		style: normalizeStyle(_ctx.innerStyle),
		"stroke-width": _ctx.strokeWidth,
		"stroke-linecap": _ctx.strokeLinecap,
		"stroke-linejoin": _ctx.strokeLinejoin,
		onClick: _cache[0] || (_cache[0] = (...args) => _ctx.onClick && _ctx.onClick(...args))
	}, _cache[1] || (_cache[1] = [
		createBaseVNode("path", { d: "M37 42H11a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h21l7 7v27a2 2 0 0 1-2 2Z" }, null, -1),
		createBaseVNode("path", {
			d: "M25 30a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z",
			fill: "currentColor",
			stroke: "none"
		}, null, -1),
		createBaseVNode("path", { d: "M25 30a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm0 0-.951-12.363a.5.5 0 0 1 .58-.532L30 18" }, null, -1)
	]), 14, _hoisted_1);
}
var _IconFileAudio = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render]]);

//#endregion
//#region node_modules/.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.17_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/icon/icon-file-audio/index.js
const IconFileAudio = Object.assign(_IconFileAudio, { install: (app, options) => {
	var _a;
	const iconPrefix = (_a = options == null ? void 0 : options.iconPrefix) != null ? _a : "";
	app.component(iconPrefix + _IconFileAudio.name, _IconFileAudio);
} });

//#endregion
export { IconCalendar, IconCaretDown, IconCaretLeft, IconCaretRight, IconCaretUp, IconCheck, IconCheckCircleFill, IconClockCircle, IconClose, IconCloseCircleFill, IconCopy, IconDelete, IconDoubleLeft, IconDoubleRight, IconDown, IconDragDot, IconDragDotVertical, IconEdit, IconEmpty, IconExclamation, IconExclamationCircleFill, IconEye, IconEyeInvisible, IconFaceFrownFill, IconFaceMehFill, IconFaceSmileFill, IconFile, IconFileAudio, IconFileImage, IconFilePdf, IconFileVideo, IconFilter, IconFullscreen, IconImageClose, IconInfo, IconInfoCircleFill, IconLeft, IconLink, IconLoading, IconMenuFold, IconMenuUnfold, IconMinus, IconMore, IconObliqueLine, IconOriginalSize, IconPause, IconPlayArrowFill, IconPlus, IconQuestionCircle, IconRight, IconRotateLeft, IconRotateRight, IconSearch, IconStarFill, IconToTop, IconUp, IconUpload, IconZoomIn, IconZoomOut, _export_sfc, configProviderInjectionKey, getComponentPrefix, getPrefixCls, isArray, isBoolean, isComponentInstance, isDayjs, isEmptyObject, isExist, isFunction, isNull, isNumber, isObject, isPromise, isQuarter, isString, isUndefined, isWindow, setGlobalConfig };
//# sourceMappingURL=icon-file-audio-Dy5Fr4FH.js.map