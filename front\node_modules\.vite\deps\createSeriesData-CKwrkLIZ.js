import { createHashMap, curry, defaults, each, extend, isArray, isFunction, isObject, isString, map, retrieve } from "./graphic-C2zhjhJD.js";
import { BE_ORDINAL, CoordinateSystem_default, CtorInt32Array, SINGLE_REFERRING, SOURCE_FORMAT_ORIGINAL, SeriesDataSchema, SeriesData_default, SeriesDimensionDefine_default, VISUAL_DIMENSIONS, createDimNameMap, createSourceFromSeriesDataOption, enableDataStack, ensureSourceDimNameMap, getDataItemValue, getDimensionTypeByAxis, guessOrdinal, isSourceInstance, makeSeriesEncodeForAxisCoordSys, normalizeToArray, shouldOmitUnusedDimensions } from "./parseGeoJson-CmOG-Ori.js";

//#region node_modules/.pnpm/echarts@5.6.0/node_modules/echarts/lib/data/helper/createDimensions.js
/**

* For outside usage compat (like echarts-gl are using it).

*/
function createDimensions(source, opt) {
	return prepareSeriesDataSchema(source, opt).dimensions;
}
/**

* This method builds the relationship between:

* + "what the coord sys or series requires (see `coordDimensions`)",

* + "what the user defines (in `encode` and `dimensions`, see `opt.dimensionsDefine` and `opt.encodeDefine`)"

* + "what the data source provids (see `source`)".

*

* Some guess strategy will be adapted if user does not define something.

* If no 'value' dimension specified, the first no-named dimension will be

* named as 'value'.

*

* @return The results are always sorted by `storeDimIndex` asc.

*/
function prepareSeriesDataSchema(source, opt) {
	if (!isSourceInstance(source)) source = createSourceFromSeriesDataOption(source);
	opt = opt || {};
	var sysDims = opt.coordDimensions || [];
	var dimsDef = opt.dimensionsDefine || source.dimensionsDefine || [];
	var coordDimNameMap = createHashMap();
	var resultList = [];
	var dimCount = getDimCount(source, sysDims, dimsDef, opt.dimensionsCount);
	var omitUnusedDimensions = opt.canOmitUnusedDimensions && shouldOmitUnusedDimensions(dimCount);
	var isUsingSourceDimensionsDef = dimsDef === source.dimensionsDefine;
	var dataDimNameMap = isUsingSourceDimensionsDef ? ensureSourceDimNameMap(source) : createDimNameMap(dimsDef);
	var encodeDef = opt.encodeDefine;
	if (!encodeDef && opt.encodeDefaulter) encodeDef = opt.encodeDefaulter(source, dimCount);
	var encodeDefMap = createHashMap(encodeDef);
	var indicesMap = new CtorInt32Array(dimCount);
	for (var i = 0; i < indicesMap.length; i++) indicesMap[i] = -1;
	function getResultItem(dimIdx) {
		var idx = indicesMap[dimIdx];
		if (idx < 0) {
			var dimDefItemRaw = dimsDef[dimIdx];
			var dimDefItem = isObject(dimDefItemRaw) ? dimDefItemRaw : { name: dimDefItemRaw };
			var resultItem$1 = new SeriesDimensionDefine_default();
			var userDimName = dimDefItem.name;
			if (userDimName != null && dataDimNameMap.get(userDimName) != null) resultItem$1.name = resultItem$1.displayName = userDimName;
			dimDefItem.type != null && (resultItem$1.type = dimDefItem.type);
			dimDefItem.displayName != null && (resultItem$1.displayName = dimDefItem.displayName);
			var newIdx = resultList.length;
			indicesMap[dimIdx] = newIdx;
			resultItem$1.storeDimIndex = dimIdx;
			resultList.push(resultItem$1);
			return resultItem$1;
		}
		return resultList[idx];
	}
	if (!omitUnusedDimensions) for (var i = 0; i < dimCount; i++) getResultItem(i);
	encodeDefMap.each(function(dataDimsRaw, coordDim$1) {
		var dataDims = normalizeToArray(dataDimsRaw).slice();
		if (dataDims.length === 1 && !isString(dataDims[0]) && dataDims[0] < 0) {
			encodeDefMap.set(coordDim$1, false);
			return;
		}
		var validDataDims = encodeDefMap.set(coordDim$1, []);
		each(dataDims, function(resultDimIdxOrName, idx) {
			var resultDimIdx$1 = isString(resultDimIdxOrName) ? dataDimNameMap.get(resultDimIdxOrName) : resultDimIdxOrName;
			if (resultDimIdx$1 != null && resultDimIdx$1 < dimCount) {
				validDataDims[idx] = resultDimIdx$1;
				applyDim(getResultItem(resultDimIdx$1), coordDim$1, idx);
			}
		});
	});
	var availDimIdx = 0;
	each(sysDims, function(sysDimItemRaw) {
		var coordDim$1;
		var sysDimItemDimsDef;
		var sysDimItemOtherDims;
		var sysDimItem;
		if (isString(sysDimItemRaw)) {
			coordDim$1 = sysDimItemRaw;
			sysDimItem = {};
		} else {
			sysDimItem = sysDimItemRaw;
			coordDim$1 = sysDimItem.name;
			var ordinalMeta = sysDimItem.ordinalMeta;
			sysDimItem.ordinalMeta = null;
			sysDimItem = extend({}, sysDimItem);
			sysDimItem.ordinalMeta = ordinalMeta;
			sysDimItemDimsDef = sysDimItem.dimsDef;
			sysDimItemOtherDims = sysDimItem.otherDims;
			sysDimItem.name = sysDimItem.coordDim = sysDimItem.coordDimIndex = sysDimItem.dimsDef = sysDimItem.otherDims = null;
		}
		var dataDims = encodeDefMap.get(coordDim$1);
		if (dataDims === false) return;
		dataDims = normalizeToArray(dataDims);
		if (!dataDims.length) for (var i$1 = 0; i$1 < (sysDimItemDimsDef && sysDimItemDimsDef.length || 1); i$1++) {
			while (availDimIdx < dimCount && getResultItem(availDimIdx).coordDim != null) availDimIdx++;
			availDimIdx < dimCount && dataDims.push(availDimIdx++);
		}
		each(dataDims, function(resultDimIdx$1, coordDimIndex) {
			var resultItem$1 = getResultItem(resultDimIdx$1);
			if (isUsingSourceDimensionsDef && sysDimItem.type != null) resultItem$1.type = sysDimItem.type;
			applyDim(defaults(resultItem$1, sysDimItem), coordDim$1, coordDimIndex);
			if (resultItem$1.name == null && sysDimItemDimsDef) {
				var sysDimItemDimsDefItem = sysDimItemDimsDef[coordDimIndex];
				!isObject(sysDimItemDimsDefItem) && (sysDimItemDimsDefItem = { name: sysDimItemDimsDefItem });
				resultItem$1.name = resultItem$1.displayName = sysDimItemDimsDefItem.name;
				resultItem$1.defaultTooltip = sysDimItemDimsDefItem.defaultTooltip;
			}
			sysDimItemOtherDims && defaults(resultItem$1.otherDims, sysDimItemOtherDims);
		});
	});
	function applyDim(resultItem$1, coordDim$1, coordDimIndex) {
		if (VISUAL_DIMENSIONS.get(coordDim$1) != null) resultItem$1.otherDims[coordDim$1] = coordDimIndex;
		else {
			resultItem$1.coordDim = coordDim$1;
			resultItem$1.coordDimIndex = coordDimIndex;
			coordDimNameMap.set(coordDim$1, true);
		}
	}
	var generateCoord = opt.generateCoord;
	var generateCoordCount = opt.generateCoordCount;
	var fromZero = generateCoordCount != null;
	generateCoordCount = generateCoord ? generateCoordCount || 1 : 0;
	var extra = generateCoord || "value";
	function ifNoNameFillWithCoordName(resultItem$1) {
		if (resultItem$1.name == null) resultItem$1.name = resultItem$1.coordDim;
	}
	if (!omitUnusedDimensions) for (var resultDimIdx = 0; resultDimIdx < dimCount; resultDimIdx++) {
		var resultItem = getResultItem(resultDimIdx);
		var coordDim = resultItem.coordDim;
		if (coordDim == null) {
			resultItem.coordDim = genCoordDimName(extra, coordDimNameMap, fromZero);
			resultItem.coordDimIndex = 0;
			if (!generateCoord || generateCoordCount <= 0) resultItem.isExtraCoord = true;
			generateCoordCount--;
		}
		ifNoNameFillWithCoordName(resultItem);
		if (resultItem.type == null && (guessOrdinal(source, resultDimIdx) === BE_ORDINAL.Must || resultItem.isExtraCoord && (resultItem.otherDims.itemName != null || resultItem.otherDims.seriesName != null))) resultItem.type = "ordinal";
	}
	else {
		each(resultList, function(resultItem$1) {
			ifNoNameFillWithCoordName(resultItem$1);
		});
		resultList.sort(function(item0, item1) {
			return item0.storeDimIndex - item1.storeDimIndex;
		});
	}
	removeDuplication(resultList);
	return new SeriesDataSchema({
		source,
		dimensions: resultList,
		fullDimensionCount: dimCount,
		dimensionOmitted: omitUnusedDimensions
	});
}
function removeDuplication(result) {
	var duplicationMap = createHashMap();
	for (var i = 0; i < result.length; i++) {
		var dim = result[i];
		var dimOriginalName = dim.name;
		var count = duplicationMap.get(dimOriginalName) || 0;
		if (count > 0) dim.name = dimOriginalName + (count - 1);
		count++;
		duplicationMap.set(dimOriginalName, count);
	}
}
function getDimCount(source, sysDims, dimsDef, optDimCount) {
	var dimCount = Math.max(source.dimensionsDetectedCount || 1, sysDims.length, dimsDef.length, optDimCount || 0);
	each(sysDims, function(sysDimItem) {
		var sysDimItemDimsDef;
		if (isObject(sysDimItem) && (sysDimItemDimsDef = sysDimItem.dimsDef)) dimCount = Math.max(dimCount, sysDimItemDimsDef.length);
	});
	return dimCount;
}
function genCoordDimName(name, map$1, fromZero) {
	if (fromZero || map$1.hasKey(name)) {
		var i = 0;
		while (map$1.hasKey(name + i)) i++;
		name += i;
	}
	map$1.set(name, true);
	return name;
}

//#endregion
//#region node_modules/.pnpm/echarts@5.6.0/node_modules/echarts/lib/model/referHelper.js
/**

* @class

* For example:

* {

*     coordSysName: 'cartesian2d',

*     coordSysDims: ['x', 'y', ...],

*     axisMap: HashMap({

*         x: xAxisModel,

*         y: yAxisModel

*     }),

*     categoryAxisMap: HashMap({

*         x: xAxisModel,

*         y: undefined

*     }),

*     // The index of the first category axis in `coordSysDims`.

*     // `null/undefined` means no category axis exists.

*     firstCategoryDimIndex: 1,

*     // To replace user specified encode.

* }

*/
var CoordSysInfo = function() {
	function CoordSysInfo$1(coordSysName) {
		this.coordSysDims = [];
		this.axisMap = createHashMap();
		this.categoryAxisMap = createHashMap();
		this.coordSysName = coordSysName;
	}
	return CoordSysInfo$1;
}();
function getCoordSysInfoBySeries(seriesModel) {
	var coordSysName = seriesModel.get("coordinateSystem");
	var result = new CoordSysInfo(coordSysName);
	var fetch = fetchers[coordSysName];
	if (fetch) {
		fetch(seriesModel, result, result.axisMap, result.categoryAxisMap);
		return result;
	}
}
var fetchers = {
	cartesian2d: function(seriesModel, result, axisMap, categoryAxisMap) {
		var xAxisModel = seriesModel.getReferringComponents("xAxis", SINGLE_REFERRING).models[0];
		var yAxisModel = seriesModel.getReferringComponents("yAxis", SINGLE_REFERRING).models[0];
		{
			if (!xAxisModel) throw new Error("xAxis \"" + retrieve(seriesModel.get("xAxisIndex"), seriesModel.get("xAxisId"), 0) + "\" not found");
			if (!yAxisModel) throw new Error("yAxis \"" + retrieve(seriesModel.get("xAxisIndex"), seriesModel.get("yAxisId"), 0) + "\" not found");
		}
		result.coordSysDims = ["x", "y"];
		axisMap.set("x", xAxisModel);
		axisMap.set("y", yAxisModel);
		if (isCategory(xAxisModel)) {
			categoryAxisMap.set("x", xAxisModel);
			result.firstCategoryDimIndex = 0;
		}
		if (isCategory(yAxisModel)) {
			categoryAxisMap.set("y", yAxisModel);
			result.firstCategoryDimIndex ??= 1;
		}
	},
	singleAxis: function(seriesModel, result, axisMap, categoryAxisMap) {
		var singleAxisModel = seriesModel.getReferringComponents("singleAxis", SINGLE_REFERRING).models[0];
		if (!singleAxisModel) throw new Error("singleAxis should be specified.");
		result.coordSysDims = ["single"];
		axisMap.set("single", singleAxisModel);
		if (isCategory(singleAxisModel)) {
			categoryAxisMap.set("single", singleAxisModel);
			result.firstCategoryDimIndex = 0;
		}
	},
	polar: function(seriesModel, result, axisMap, categoryAxisMap) {
		var polarModel = seriesModel.getReferringComponents("polar", SINGLE_REFERRING).models[0];
		var radiusAxisModel = polarModel.findAxisModel("radiusAxis");
		var angleAxisModel = polarModel.findAxisModel("angleAxis");
		{
			if (!angleAxisModel) throw new Error("angleAxis option not found");
			if (!radiusAxisModel) throw new Error("radiusAxis option not found");
		}
		result.coordSysDims = ["radius", "angle"];
		axisMap.set("radius", radiusAxisModel);
		axisMap.set("angle", angleAxisModel);
		if (isCategory(radiusAxisModel)) {
			categoryAxisMap.set("radius", radiusAxisModel);
			result.firstCategoryDimIndex = 0;
		}
		if (isCategory(angleAxisModel)) {
			categoryAxisMap.set("angle", angleAxisModel);
			result.firstCategoryDimIndex ??= 1;
		}
	},
	geo: function(seriesModel, result, axisMap, categoryAxisMap) {
		result.coordSysDims = ["lng", "lat"];
	},
	parallel: function(seriesModel, result, axisMap, categoryAxisMap) {
		var ecModel = seriesModel.ecModel;
		var parallelModel = ecModel.getComponent("parallel", seriesModel.get("parallelIndex"));
		var coordSysDims = result.coordSysDims = parallelModel.dimensions.slice();
		each(parallelModel.parallelAxisIndex, function(axisIndex, index) {
			var axisModel = ecModel.getComponent("parallelAxis", axisIndex);
			var axisDim = coordSysDims[index];
			axisMap.set(axisDim, axisModel);
			if (isCategory(axisModel)) {
				categoryAxisMap.set(axisDim, axisModel);
				if (result.firstCategoryDimIndex == null) result.firstCategoryDimIndex = index;
			}
		});
	}
};
function isCategory(axisModel) {
	return axisModel.get("type") === "category";
}

//#endregion
//#region node_modules/.pnpm/echarts@5.6.0/node_modules/echarts/lib/chart/helper/createSeriesData.js
function getCoordSysDimDefs(seriesModel, coordSysInfo) {
	var coordSysName = seriesModel.get("coordinateSystem");
	var registeredCoordSys = CoordinateSystem_default.get(coordSysName);
	var coordSysDimDefs;
	if (coordSysInfo && coordSysInfo.coordSysDims) coordSysDimDefs = map(coordSysInfo.coordSysDims, function(dim) {
		var dimInfo = { name: dim };
		var axisModel = coordSysInfo.axisMap.get(dim);
		if (axisModel) {
			var axisType = axisModel.get("type");
			dimInfo.type = getDimensionTypeByAxis(axisType);
		}
		return dimInfo;
	});
	if (!coordSysDimDefs) coordSysDimDefs = registeredCoordSys && (registeredCoordSys.getDimensionsInfo ? registeredCoordSys.getDimensionsInfo() : registeredCoordSys.dimensions.slice()) || ["x", "y"];
	return coordSysDimDefs;
}
function injectOrdinalMeta(dimInfoList, createInvertedIndices, coordSysInfo) {
	var firstCategoryDimIndex;
	var hasNameEncode;
	coordSysInfo && each(dimInfoList, function(dimInfo, dimIndex) {
		var coordDim = dimInfo.coordDim;
		var categoryAxisModel = coordSysInfo.categoryAxisMap.get(coordDim);
		if (categoryAxisModel) {
			if (firstCategoryDimIndex == null) firstCategoryDimIndex = dimIndex;
			dimInfo.ordinalMeta = categoryAxisModel.getOrdinalMeta();
			if (createInvertedIndices) dimInfo.createInvertedIndices = true;
		}
		if (dimInfo.otherDims.itemName != null) hasNameEncode = true;
	});
	if (!hasNameEncode && firstCategoryDimIndex != null) dimInfoList[firstCategoryDimIndex].otherDims.itemName = 0;
	return firstCategoryDimIndex;
}
/**

* Caution: there are side effects to `sourceManager` in this method.

* Should better only be called in `Series['getInitialData']`.

*/
function createSeriesData(sourceRaw, seriesModel, opt) {
	opt = opt || {};
	var sourceManager = seriesModel.getSourceManager();
	var source;
	var isOriginalSource = false;
	if (sourceRaw) {
		isOriginalSource = true;
		source = createSourceFromSeriesDataOption(sourceRaw);
	} else {
		source = sourceManager.getSource();
		isOriginalSource = source.sourceFormat === SOURCE_FORMAT_ORIGINAL;
	}
	var coordSysInfo = getCoordSysInfoBySeries(seriesModel);
	var coordSysDimDefs = getCoordSysDimDefs(seriesModel, coordSysInfo);
	var useEncodeDefaulter = opt.useEncodeDefaulter;
	var encodeDefaulter = isFunction(useEncodeDefaulter) ? useEncodeDefaulter : useEncodeDefaulter ? curry(makeSeriesEncodeForAxisCoordSys, coordSysDimDefs, seriesModel) : null;
	var createDimensionOptions = {
		coordDimensions: coordSysDimDefs,
		generateCoord: opt.generateCoord,
		encodeDefine: seriesModel.getEncode(),
		encodeDefaulter,
		canOmitUnusedDimensions: !isOriginalSource
	};
	var schema = prepareSeriesDataSchema(source, createDimensionOptions);
	var firstCategoryDimIndex = injectOrdinalMeta(schema.dimensions, opt.createInvertedIndices, coordSysInfo);
	var store = !isOriginalSource ? sourceManager.getSharedDataStore(schema) : null;
	var stackCalculationInfo = enableDataStack(seriesModel, {
		schema,
		store
	});
	var data = new SeriesData_default(schema, seriesModel);
	data.setCalculationInfo(stackCalculationInfo);
	var dimValueGetter = firstCategoryDimIndex != null && isNeedCompleteOrdinalData(source) ? function(itemOpt, dimName, dataIndex, dimIndex) {
		return dimIndex === firstCategoryDimIndex ? dataIndex : this.defaultDimValueGetter(itemOpt, dimName, dataIndex, dimIndex);
	} : null;
	data.hasItemOption = false;
	data.initData(isOriginalSource ? source : store, null, dimValueGetter);
	return data;
}
function isNeedCompleteOrdinalData(source) {
	if (source.sourceFormat === SOURCE_FORMAT_ORIGINAL) {
		var sampleItem = firstDataNotNull(source.data || []);
		return !isArray(getDataItemValue(sampleItem));
	}
}
function firstDataNotNull(arr) {
	var i = 0;
	while (i < arr.length && arr[i] == null) i++;
	return arr[i];
}
var createSeriesData_default = createSeriesData;

//#endregion
export { createDimensions, createSeriesData_default, prepareSeriesDataSchema };
//# sourceMappingURL=createSeriesData-CKwrkLIZ.js.map