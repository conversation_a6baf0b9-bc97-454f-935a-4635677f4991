import axios from 'axios';

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// 类型定义
export interface HousingListing {
  id: string;
  listingId: string;
  source: string;
  title: string;
  link: string;
  community: string;
  district: string;
  totalPrice: number;
  unitPrice: number;
  area: number;
  layout: string;
  floor?: string;
  orientation?: string;
  buildYear?: number;
  imageUrl?: string;
  firstScrapedAt: string;
  lastScrapedAt: string;
}

export interface PriceHistory {
  id: string;
  price: number;
  scrapedAt: string;
}

export interface HousingListingDetail extends HousingListing {
  priceHistory: PriceHistory[];
}

export interface Subscription {
  id: string;
  subscriptionType: 'NEW_LISTING' | 'PRICE_CHANGE';
  keywords?: string;
  isActive: boolean;
  createdAt: string;
  listing?: HousingListing;
}

export interface PaginationResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ListingFilters {
  page?: number;
  limit?: number;
  source?: string;
  district?: string;
  community?: string;
  minPrice?: number;
  maxPrice?: number;
  minArea?: number;
  maxArea?: number;
  layout?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface StatsResponse {
  totalListings: number;
  avgTotalPrice: number;
  avgUnitPrice: number;
  minPrice: number;
  maxPrice: number;
  sourceStats: Array<{
    source: string;
    count: number;
  }>;
}

export interface DistrictStats {
  district: string;
  avgTotalPrice: number;
  avgUnitPrice: number;
  count: number;
}

// API方法
export const listingApi = {
  // 获取房源列表
  getListings: (filters: ListingFilters = {}): Promise<PaginationResponse<HousingListing>> => {
    return apiClient.get('/api/listings', { params: filters }).then(res => res.data);
  },

  // 获取房源详情
  getListingDetail: (id: string): Promise<HousingListingDetail> => {
    return apiClient.get(`/api/listings/${id}`).then(res => res.data);
  },

  // 获取统计摘要
  getStats: (): Promise<StatsResponse> => {
    return apiClient.get('/api/stats/summary').then(res => res.data);
  },

  // 获取区域均价
  getDistrictStats: (): Promise<DistrictStats[]> => {
    return apiClient.get('/api/stats/district_avg_price').then(res => res.data);
  },

  // 获取价格趋势
  getPriceTrend: (days: number = 30): Promise<Array<{
    date: string;
    avgPrice: number;
    count: number;
  }>> => {
    return apiClient.get('/api/stats/price_trend', { params: { days } }).then(res => res.data);
  },
};

export const subscriptionApi = {
  // 获取订阅列表
  getSubscriptions: (params: {
    page?: number;
    limit?: number;
    type?: 'NEW_LISTING' | 'PRICE_CHANGE';
    active?: boolean;
  } = {}): Promise<PaginationResponse<Subscription>> => {
    return apiClient.get('/api/subscriptions', { params }).then(res => res.data);
  },

  // 创建订阅
  createSubscription: (data: {
    subscriptionType: 'NEW_LISTING' | 'PRICE_CHANGE';
    listingId?: string;
    keywords?: string;
  }): Promise<Subscription> => {
    return apiClient.post('/api/subscriptions', data).then(res => res.data);
  },

  // 更新订阅
  updateSubscription: (id: string, data: {
    keywords?: string;
    isActive?: boolean;
  }): Promise<Subscription> => {
    return apiClient.put(`/api/subscriptions/${id}`, data).then(res => res.data);
  },

  // 删除订阅
  deleteSubscription: (id: string): Promise<{ success: boolean; message: string }> => {
    return apiClient.delete(`/api/subscriptions/${id}`).then(res => res.data);
  },
};

export const scraperApi = {
  // 手动运行爬虫
  runScraper: (data: {
    source?: string;
    pages?: number;
  } = {}): Promise<{
    success: boolean;
    message: string;
    timestamp: string;
  }> => {
    return apiClient.post('/api/scraper/run', data).then(res => res.data);
  },

  // 获取爬虫状态
  getScraperStatus: (): Promise<{
    status: string;
    lastRun: string;
    nextRun: string;
    availableSources: string[];
  }> => {
    return apiClient.get('/api/scraper/status').then(res => res.data);
  },
};

export default apiClient;
