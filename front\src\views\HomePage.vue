<template>
  <div class="home-page">
    <!-- 统计卡片 -->
    <a-row :gutter="24" class="stats-row">
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="总房源数"
            :value="stats?.totalListings || 0"
            :value-style="{ color: '#165dff' }"
          >
            <template #prefix>
              <icon-home />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="平均总价"
            :value="stats?.avgTotalPrice || 0"
            :precision="1"
            suffix="万"
            :value-style="{ color: '#f53f3f' }"
          >
            <template #prefix>
              <icon-tag />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="平均单价"
            :value="stats?.avgUnitPrice || 0"
            :precision="0"
            suffix="元/㎡"
            :value-style="{ color: '#00b42a' }"
          >
            <template #prefix>
              <icon-calculator />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
         
          <a-statistic
            title="价格区间"
            :extra="`${stats?.minPrice || 0} - ${stats?.maxPrice || 0}`"
            suffix="万"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <icon-bar-chart />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 主要内容区域 -->
    <a-row :gutter="24" class="content-row">
      <!-- 最新房源 -->
      <a-col :span="16">
        <a-card title="最新房源" class="latest-listings">
          <template #extra>
            <a-space>
              <span class="listings-count">
                共 {{ stats?.totalListings || 0 }} 套房源
              </span>
              <a-button type="text" @click="goToListings">
                查看全部
                <template #icon>
                  <icon-right />
                </template>
              </a-button>
            </a-space>
          </template>

          <a-spin :loading="listingsLoading" style="width: 100%">
            <div v-if="hasListings" class="listings-grid">
              <listing-card
                v-for="listing in latestListings"
                :key="listing.id"
                :listing="listing"
                class="listing-item"
              />
            </div>
            <a-empty v-else description="暂无房源数据">
              <template #image>
                <icon-home style="font-size: 64px; color: #c9cdd4;" />
              </template>
              <template #description>
                <div>
                  <p>暂无房源数据</p>
                  <a-button type="primary" @click="handleRunScraper" :loading="scraperLoading">
                    立即抓取数据
                  </a-button>
                </div>
              </template>
            </a-empty>
          </a-spin>

          <!-- 快速分页导航 -->
          <div v-if="hasListings" class="quick-pagination">
            <a-space>
              <span class="pagination-info">显示最新 {{ latestListings.length }} 套房源</span>
              <a-button size="small" @click="loadMoreListings" :loading="listingsLoading">
                加载更多
              </a-button>
            </a-space>
          </div>
        </a-card>
      </a-col>

      <!-- 侧边栏 -->
      <a-col :span="8">
        <!-- 数据来源统计 -->
        <a-card title="数据来源" class="source-stats">
          <div v-if="stats?.sourceStats" class="source-list">
            <div
              v-for="source in stats.sourceStats"
              :key="source.source"
              class="source-item"
            >
              <div class="source-info">
                <a-tag :color="getSourceColor(source.source)">
                  {{ getSourceName(source.source) }}
                </a-tag>
                <span class="source-count">{{ source.count }} 套</span>
              </div>
              <a-progress
                :percent="(source.count / (stats?.totalListings || 1)) * 100"
                :show-text="false"
                size="small"
                :color="getSourceColor(source.source)"
              />
            </div>
          </div>
        </a-card>

        <!-- 快速操作 -->
        <a-card title="快速操作" class="quick-actions">
          <a-space direction="vertical" fill>
            <a-button
              type="primary"
              long
              @click="handleRunScraper"
              :loading="scraperLoading"
            >
              <template #icon>
                <icon-robot />
              </template>
              手动运行爬虫
            </a-button>
            <a-button long @click="goToSubscriptions">
              <template #icon>
                <icon-notification />
              </template>
              管理订阅
            </a-button>
            <a-button long @click="goToAnalysis">
              <template #icon>
                <icon-bar-chart />
              </template>
              数据分析
            </a-button>
          </a-space>
        </a-card>

        <!-- 系统状态 -->
        <a-card title="系统状态" class="system-status">
          <a-space direction="vertical" fill>
            <div class="status-item">
              <span class="status-label">爬虫状态:</span>
              <a-tag color="green">运行中</a-tag>
            </div>
            <div class="status-item">
              <span class="status-label">下次运行:</span>
              <span class="status-value">6小时后</span>
            </div>
            <div class="status-item">
              <span class="status-label">最后更新:</span>
              <span class="status-value">{{ formatTime(new Date()) }}</span>
            </div>
          </a-space>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import {
  IconHome,
  IconTag,

  IconBarChart,
  IconRight,
  IconRobot,
  IconNotification
} from '@arco-design/web-vue/es/icon';
import { useListingsStore } from '../store/listings';
import { scraperApi } from '../api/client';
import ListingCard from '../components/ListingCard.vue';

const router = useRouter();
const listingsStore = useListingsStore();

// 状态
const scraperLoading = ref(false);

// 计算属性
const stats = computed(() => listingsStore.stats);
const latestListings = computed(() => listingsStore.listings.slice(0, 6));
const hasListings = computed(() => listingsStore.hasListings);
const listingsLoading = computed(() => listingsStore.loading);

// 获取数据来源颜色
const getSourceColor = (source: string) => {
  switch (source) {
    case 'beike':
      return 'green';
    case 'anjuke':
      return 'blue';
    default:
      return 'gray';
  }
};

// 获取数据来源名称
const getSourceName = (source: string) => {
  switch (source) {
    case 'beike':
      return '贝壳网';
    case 'anjuke':
      return '安居客';
    default:
      return source;
  }
};

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleString();
};

// 页面跳转
const goToListings = () => {
  router.push('/listings');
};

const goToSubscriptions = () => {
  router.push('/subscriptions');
};

const goToAnalysis = () => {
  router.push('/analysis');
};

const loadMoreListings = () => {
  router.push('/listings');
};

// 手动运行爬虫
const handleRunScraper = async () => {
  try {
    scraperLoading.value = true;
    const result = await scraperApi.runScraper({ pages: 1 });
    
    if (result.success) {
      Message.success('爬虫任务已启动，请稍后查看数据更新');
      // 延迟刷新数据
      setTimeout(() => {
        loadData();
      }, 3000);
    } else {
      Message.error(result.message || '爬虫任务启动失败');
    }
  } catch (error) {
    console.error('Error running scraper:', error);
    Message.error('爬虫任务启动失败');
  } finally {
    scraperLoading.value = false;
  }
};

// 加载数据
const loadData = async () => {
  try {
    await Promise.all([
      listingsStore.fetchStats(),
      listingsStore.fetchListings({ limit: 6 })
    ]);
  } catch (error) {
    console.error('Error loading data:', error);
    Message.error('数据加载失败');
  }
};

// 页面挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.home-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.content-row {
  margin-bottom: 24px;
}

.latest-listings {
  margin-bottom: 24px;
}

.listings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.source-stats,
.quick-actions,
.system-status {
  margin-bottom: 16px;
}

.source-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.source-item {
  margin-bottom: 12px;
}

.source-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.source-count {
  font-size: 14px;
  color: #86909c;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  color: #86909c;
  font-size: 14px;
}

.status-value {
  color: #4e5969;
  font-size: 14px;
}

.listings-count {
  font-size: 12px;
  color: #86909c;
  background: #f2f3f5;
  padding: 2px 8px;
  border-radius: 4px;
}

.quick-pagination {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f2f3f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-info {
  font-size: 12px;
  color: #86909c;
}

:deep(.arco-statistic-title) {
  font-size: 14px;
  color: #86909c;
}

:deep(.arco-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}
</style>
