import { __export } from "./chunk-51aI8Tpl.js";
import { BoundingRect_default, CompoundPath_default, Group_default, Image_default, Rect_default, Text_default, bind, clone$2 as clone, curry, defaults, each, encodeHTML, extend, filter, indexOf, inherits, isArray, isFunction, isObject, isString, map, merge, mixin, reduce, truncateText } from "./graphic-C2zhjhJD.js";
import { Arc_default, AxisModelCommonMixin, BezierCurve_default, Chart_default, Circle_default, Component_default as Component_default$1, Component_default$1 as Component_default, Ellipse_default, IncrementalDisplayable_default, Line_default, LinearGradient_default, MAX_SAFE_INTEGER, Model_default, Polygon_default, Polyline_default, RadialGradient_default, Ring_default, Sector_default, Series_default, addCommas, asc, capitalFirst, clipPointsByRect, clipRectByRect, createIcon, createScaleByModel, createSymbol, createTextStyle, enableDataStack, enableHoverEmphasis, extendPath, extendShape, format, formatTime, formatTpl, getECData, getLayoutRect, getPercentWithPrecision, getPixelPrecision, getPrecision, getPrecisionSafe, getShapeClass, getStackedDimension, getTextRect, getTooltipMarker, getTransform, initProps, isDimensionStacked, isNumeric, isRadianAroundZero, linearMap, makeImage, makePath, mergePath, nice, niceScaleExtent, normalizeCssArray, numericToNumber, parseDate, quantile, quantity, quantityExponent, reformIntervals, registerShape, remRadian, resizePath, round, toCamelCase, updateProps } from "./parseGeoJson-CmOG-Ori.js";
import { createDimensions, createSeriesData_default } from "./createSeriesData-CKwrkLIZ.js";

//#region node_modules/.pnpm/echarts@5.6.0/node_modules/echarts/lib/export/api/helper.js
var helper_exports = {};
__export(helper_exports, {
	createDimensions: () => createDimensions,
	createList: () => createList,
	createScale: () => createScale,
	createSymbol: () => createSymbol,
	createTextStyle: () => createTextStyle$1,
	dataStack: () => dataStack,
	enableHoverEmphasis: () => enableHoverEmphasis,
	getECData: () => getECData,
	getLayoutRect: () => getLayoutRect,
	mixinAxisModelCommonMethods: () => mixinAxisModelCommonMethods
});
/**

* Create a multi dimension List structure from seriesModel.

*/
function createList(seriesModel) {
	return createSeriesData_default(null, seriesModel);
}
var dataStack = {
	isDimensionStacked,
	enableDataStack,
	getStackedDimension
};
/**

* Create scale

* @param {Array.<number>} dataExtent

* @param {Object|module:echarts/Model} option If `optoin.type`

*        is secified, it can only be `'value'` currently.

*/
function createScale(dataExtent, option) {
	var axisModel = option;
	if (!(option instanceof Model_default)) axisModel = new Model_default(option);
	var scale = createScaleByModel(axisModel);
	scale.setExtent(dataExtent[0], dataExtent[1]);
	niceScaleExtent(scale, axisModel);
	return scale;
}
/**

* Mixin common methods to axis model,

*

* Include methods

* `getFormattedLabels() => Array.<string>`

* `getCategories() => Array.<string>`

* `getMin(origin: boolean) => number`

* `getMax(origin: boolean) => number`

* `getNeedCrossZero() => boolean`

*/
function mixinAxisModelCommonMethods(Model) {
	mixin(Model, AxisModelCommonMixin);
}
function createTextStyle$1(textStyleModel, opts) {
	opts = opts || {};
	return createTextStyle(textStyleModel, null, null, opts.state !== "normal");
}

//#endregion
//#region node_modules/.pnpm/echarts@5.6.0/node_modules/echarts/lib/export/api/number.js
var number_exports = {};
__export(number_exports, {
	MAX_SAFE_INTEGER: () => MAX_SAFE_INTEGER,
	asc: () => asc,
	getPercentWithPrecision: () => getPercentWithPrecision,
	getPixelPrecision: () => getPixelPrecision,
	getPrecision: () => getPrecision,
	getPrecisionSafe: () => getPrecisionSafe,
	isNumeric: () => isNumeric,
	isRadianAroundZero: () => isRadianAroundZero,
	linearMap: () => linearMap,
	nice: () => nice,
	numericToNumber: () => numericToNumber,
	parseDate: () => parseDate,
	quantile: () => quantile,
	quantity: () => quantity,
	quantityExponent: () => quantityExponent,
	reformIntervals: () => reformIntervals,
	remRadian: () => remRadian,
	round: () => round
});

//#endregion
//#region node_modules/.pnpm/echarts@5.6.0/node_modules/echarts/lib/export/api/time.js
var time_exports = {};
__export(time_exports, {
	format: () => format,
	parse: () => parseDate
});

//#endregion
//#region node_modules/.pnpm/echarts@5.6.0/node_modules/echarts/lib/export/api/graphic.js
var graphic_exports = {};
__export(graphic_exports, {
	Arc: () => Arc_default,
	BezierCurve: () => BezierCurve_default,
	BoundingRect: () => BoundingRect_default,
	Circle: () => Circle_default,
	CompoundPath: () => CompoundPath_default,
	Ellipse: () => Ellipse_default,
	Group: () => Group_default,
	Image: () => Image_default,
	IncrementalDisplayable: () => IncrementalDisplayable_default,
	Line: () => Line_default,
	LinearGradient: () => LinearGradient_default,
	Polygon: () => Polygon_default,
	Polyline: () => Polyline_default,
	RadialGradient: () => RadialGradient_default,
	Rect: () => Rect_default,
	Ring: () => Ring_default,
	Sector: () => Sector_default,
	Text: () => Text_default,
	clipPointsByRect: () => clipPointsByRect,
	clipRectByRect: () => clipRectByRect,
	createIcon: () => createIcon,
	extendPath: () => extendPath,
	extendShape: () => extendShape,
	getShapeClass: () => getShapeClass,
	getTransform: () => getTransform,
	initProps: () => initProps,
	makeImage: () => makeImage,
	makePath: () => makePath,
	mergePath: () => mergePath,
	registerShape: () => registerShape,
	resizePath: () => resizePath,
	updateProps: () => updateProps
});

//#endregion
//#region node_modules/.pnpm/echarts@5.6.0/node_modules/echarts/lib/export/api/format.js
var format_exports = {};
__export(format_exports, {
	addCommas: () => addCommas,
	capitalFirst: () => capitalFirst,
	encodeHTML: () => encodeHTML,
	formatTime: () => formatTime,
	formatTpl: () => formatTpl,
	getTextRect: () => getTextRect,
	getTooltipMarker: () => getTooltipMarker,
	normalizeCssArray: () => normalizeCssArray,
	toCamelCase: () => toCamelCase,
	truncateText: () => truncateText
});

//#endregion
//#region node_modules/.pnpm/echarts@5.6.0/node_modules/echarts/lib/export/api/util.js
var util_exports$1 = {};
__export(util_exports$1, {
	bind: () => bind,
	clone: () => clone,
	curry: () => curry,
	defaults: () => defaults,
	each: () => each,
	extend: () => extend,
	filter: () => filter,
	indexOf: () => indexOf,
	inherits: () => inherits,
	isArray: () => isArray,
	isFunction: () => isFunction,
	isObject: () => isObject,
	isString: () => isString,
	map: () => map,
	merge: () => merge,
	reduce: () => reduce
});

//#endregion
//#region node_modules/.pnpm/echarts@5.6.0/node_modules/echarts/lib/export/api.js
function extendComponentModel(proto) {
	var Model = Component_default.extend(proto);
	Component_default.registerClass(Model);
	return Model;
}
function extendComponentView(proto) {
	var View = Component_default$1.extend(proto);
	Component_default$1.registerClass(View);
	return View;
}
function extendSeriesModel(proto) {
	var Model = Series_default.extend(proto);
	Series_default.registerClass(Model);
	return Model;
}
function extendChartView(proto) {
	var View = Chart_default.extend(proto);
	Chart_default.registerClass(View);
	return View;
}

//#endregion
export { extendChartView, extendComponentModel, extendComponentView, extendSeriesModel, format_exports, graphic_exports, helper_exports, number_exports, time_exports, util_exports$1 as util_exports };
//# sourceMappingURL=core-BzFVH-54.js.map