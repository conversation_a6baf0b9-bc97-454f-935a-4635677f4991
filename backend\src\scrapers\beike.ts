import * as cheerio from 'cheerio';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface BeikeListingData {
  listingId: string;
  title: string;
  link: string;
  community: string;
  district: string;
  totalPrice: number;
  unitPrice: number;
  area: number;
  layout: string;
  floor?: string;
  orientation?: string;
  buildYear?: number;
  imageUrl?: string;
}

export class BeikeScraper {
  private targetUrls = [
    'https://nj.ke.com/ershoufang/dongshanzhen/co41l3/',
    'https://nj.ke.com/ershoufang/kexueyuan/co41l3/',
    'https://nj.ke.com/ershoufang/baijiahu/co41l3/',
    'https://nj.ke.com/ershoufang/chalukou1/co41l3/'
  ];

  private cookies = 'ianjia_uuid=2b51babe-d867-42e1-98dd-7d186d7ef225; select_city=320100; Hm_lvt_b160d5571570fd63c347b9d4ab5ca610=**********; HMACCOUNT=4F560A707B13D281; sajssdk_2015_cross_new_user=1; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22197f2339ef11231-0ea8c1911730ac8-********-1600000-197f2339ef21191%22%2C%22%24device_id%22%3A%22197f2339ef11231-0ea8c1911730ac8-********-1600000-197f2339ef21191%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fwww.google.com%2F%22%2C%22%24latest_referrer_host%22%3A%22www.google.com%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%7D%7D; lianjia_ssid=c1b2c923-46bb-495a-a136-34f79f3a4930; Qs_lvt_200116=**********%2C1752126126; crosSdkDT2019DeviceId=lgofp--i8b8r9-qtrrfwdijuves2r-h15qtyvpf; login_ucid=****************; lianjia_token=2.0015bb7b6c691c8dca0416525d52c06cf4; lianjia_token_secure=2.0015bb7b6c691c8dca0416525d52c06cf4; security_ticket=XciWgpR8LxE9p907mLV/5eJeIkc+JIv4rScYfmZgnL2HcMZfiA07bx+wmp/zM7EYg4GN+v4WPhuG2c5Bcwr4s0mizM6Vncef8AuVqAE3Hbv5lLqbWXqIkXmASVu8paE2DTwdA9VvP/CGTiJ8TxiH/L7cjS0Cieh/VMwyMbduoOw=; ftkrc_=c8c598f4-dfbc-43cf-aaf7-07755665082d; lfrc_=9e79189d-4420-4167-be9e-8176b68f0856; Qs_pv_200116=165013210371162800%2C4509264773033476000%2C1453614251677021000%2C318411130519784800%2C203065761602544000; Hm_lpvt_b160d5571570fd63c347b9d4ab5ca610=1752126449; srcid=eyJ0Ijoie1wiZGF0YVwiOlwiYzIyMTc2YTYwNDE0M2EyODk5MmY3MmZjMzEyZjg4YmU3ODc5MWEyNjhkNjBlYWQ4MTZhZThmODI5YzUwYWZlZjYzNTI2NmU5NjZlODk1NmNlMDZlZmUzMGY5NGYwNjlkNTAxODQ3MjEzZjBjMjMwMTY3Mzk4YWZiYjg4YjcxZDlkODM3ZDZiMTYzZDllNmQ5MzRiODE4YWVkZjk4NDdiOGU1YjNhMTllZDgxMjAzNzg1ZGY4NzQ4OGZiMjMyMzg3MGRkMjhlMmI1YjFkMmE2MjZhMjQwNDhlZDhkMjAyNzk3MjQ2YWU4ODMyYWMwZTkyMzlkZmUwMjA0YzgxYjI1YVwiLFwia2V5X2lkXCI6XCIxXCIsXCJzaWduXCI6XCI3NDNjY2Q2ZVwifSIsInIiOiJodHRwczovL25qLmtlLmNvbS9lcnNob3VmYW5nL2NoYWx1a291MS9jbzQxbDMvIiwib3MiOiJ3ZWIiLCJ2IjoiMC4xIn0=';

  private userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  ];

  private getRandomUserAgent(): string {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getRandomDelay(): number {
    const min = parseInt(process.env.SCRAPER_DELAY_MIN || '1000');
    const max = parseInt(process.env.SCRAPER_DELAY_MAX || '3000');
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  async scrapeUrl(url: string): Promise<BeikeListingData[]> {
    try {
      console.log(`Scraping Beike URL: ${url}`);

      const response = await fetch(url, {
        headers: {
          'User-Agent': this.getRandomUserAgent(),
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Cookie': this.cookies,
          'Referer': 'https://nj.ke.com/',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const html = await response.text();
      const $ = cheerio.load(html);
      const listings: BeikeListingData[] = [];

      // 使用新的HTML结构解析
      $('.content li').each((_, element) => {
        try {
          const $item = $(element);

          // 提取房源ID和链接
          const link = $item.find('.title a').attr('href') ||
                      $item.find('a.CLICKDATA').attr('href') || '';
          const listingIdMatch = link.match(/\/(\d+)\.html/);
          if (!listingIdMatch) return;

          const listingId = `beike_${listingIdMatch[1]}`;

          // 提取基本信息
          const title = $item.find('.title a').text().trim() ||
                       $item.find('a.CLICKDATA').attr('title') || '';
          const fullLink = link.startsWith('http') ? link : `https://nj.ke.com${link}`;

          // 提取小区和区域信息
          const positionInfo = $item.find('.positionInfo a');
          const community = positionInfo.first().text().trim();

          // 从URL或其他地方提取区域信息
          let district = '';
          if (url.includes('dongshanzhen')) district = '东山街道';
          else if (url.includes('kexueyuan')) district = '科学园';
          else if (url.includes('baijiahu')) district = '白家湖';
          else if (url.includes('chalukou1')) district = '岔路口';

          // 提取价格信息
          const totalPriceText = $item.find('.totalPrice span').text().trim() ||
                                $item.find('.totalPrice2 span').text().trim();
          const totalPrice = parseFloat(totalPriceText) || 0;

          const unitPriceText = $item.find('.unitPrice span').text().replace(/[^\d]/g, '');
          const unitPrice = parseFloat(unitPriceText) || 0;

          // 提取房屋信息
          const houseInfo = $item.find('.houseInfo').text().trim();
          const houseInfoParts = houseInfo.split('|').map(part => part.trim());

          // 解析户型和面积
          let layout = '';
          let area = 0;
          let orientation = '';
          let floor = '';

          for (const part of houseInfoParts) {
            if (part.includes('室') && part.includes('厅')) {
              layout = part.trim();
            } else if (part.includes('平米')) {
              const areaMatch = part.match(/([\d.]+)平米/);
              if (areaMatch) {
                area = parseFloat(areaMatch[1]);
              }
            } else if (part.includes('南') || part.includes('北') || part.includes('东') || part.includes('西')) {
              orientation = part.trim();
            } else if (part.includes('层')) {
              floor = part.trim();
            }
          }

          // 提取图片
          const imageUrl = $item.find('.lj-lazy').attr('data-original') ||
                          $item.find('img').attr('src') || '';

          if (title && totalPrice > 0 && area > 0) {
            listings.push({
              listingId,
              title: title.substring(0, 250), // 限制标题长度
              link: fullLink.substring(0, 500), // 限制链接长度
              community: (community || '未知小区').substring(0, 95), // 限制小区名长度
              district: district.substring(0, 45), // 限制区域名长度
              totalPrice,
              unitPrice,
              area,
              layout: (layout || '未知户型').substring(0, 95), // 限制户型长度
              floor: floor ? floor.substring(0, 45) : undefined, // 限制楼层长度
              orientation: orientation ? orientation.substring(0, 45) : undefined, // 限制朝向长度
              buildYear: undefined,
              imageUrl: imageUrl ? imageUrl.substring(0, 500) : undefined // 限制图片URL长度
            });
          }
        } catch (error) {
          console.error('Error parsing listing item:', error);
        }
      });

      console.log(`Scraped ${listings.length} listings from ${url}`);
      return listings;
    } catch (error) {
      console.error(`Error scraping ${url}:`, error);
      return [];
    }
  }

  async scrapePage(page: number = 1): Promise<BeikeListingData[]> {
    // 废弃旧方法，使用新的URL爬取方式
    console.log('scrapePage is deprecated, use scrapeAllUrls instead');
    return [];
  }

  async scrapeAllUrls(): Promise<BeikeListingData[]> {
    const allListings: BeikeListingData[] = [];

    for (const url of this.targetUrls) {
      const listings = await this.scrapeUrl(url);
      allListings.push(...listings);

      // 随机延迟避免被封
      await this.delay(this.getRandomDelay());
    }

    return allListings;
  }

  async scrapeMultiplePages(maxPages: number = 3): Promise<BeikeListingData[]> {
    // 使用新的URL爬取方式，忽略maxPages参数
    console.log('Using new URL-based scraping method');
    return await this.scrapeAllUrls();
  }

  async saveListings(listings: BeikeListingData[]): Promise<void> {
    for (const listing of listings) {
      try {
        await prisma.housingListing.upsert({
          where: { listingId: listing.listingId },
          update: {
            title: listing.title,
            link: listing.link,
            community: listing.community,
            district: listing.district,
            totalPrice: listing.totalPrice,
            unitPrice: listing.unitPrice,
            area: listing.area,
            layout: listing.layout,
            floor: listing.floor,
            orientation: listing.orientation,
            buildYear: listing.buildYear,
            imageUrl: listing.imageUrl,
            lastScrapedAt: new Date(),
            isActive: true
          },
          create: {
            listingId: listing.listingId,
            source: 'beike',
            title: listing.title,
            link: listing.link,
            community: listing.community,
            district: listing.district,
            totalPrice: listing.totalPrice,
            unitPrice: listing.unitPrice,
            area: listing.area,
            layout: listing.layout,
            floor: listing.floor,
            orientation: listing.orientation,
            buildYear: listing.buildYear,
            imageUrl: listing.imageUrl,
            isActive: true
          }
        });

        // 记录价格历史
        const existingListing = await prisma.housingListing.findUnique({
          where: { listingId: listing.listingId },
          include: { priceHistory: { orderBy: { scrapedAt: 'desc' }, take: 1 } }
        });

        if (existingListing && existingListing.priceHistory.length > 0) {
          const lastPrice = existingListing.priceHistory[0].price;
          if (Math.abs(lastPrice - listing.totalPrice) > 0.01) {
            await prisma.priceHistory.create({
              data: {
                listingId: existingListing.id,
                price: listing.totalPrice
              }
            });
          }
        } else if (existingListing) {
          await prisma.priceHistory.create({
            data: {
              listingId: existingListing.id,
              price: listing.totalPrice
            }
          });
        }
      } catch (error) {
        console.error(`Error saving listing ${listing.listingId}:`, error);
      }
    }
  }
}
